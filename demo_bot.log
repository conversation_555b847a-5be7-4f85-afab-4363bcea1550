2025-06-04 03:54:10,051 - root - INFO - Bot Status: Logged in as MissMinutesBot#1234 (ID: 123456789)
2025-06-04 03:54:10,051 - root - INFO - Started operation queue with 50 workers
2025-06-04 03:54:10,051 - root - INFO - Started worker: worker-0
2025-06-04 03:54:10,051 - root - INFO - Started worker: worker-1
2025-06-04 03:54:10,051 - root - INFO - Performance manager started
2025-06-04 03:54:10,051 - root - INFO - Enhanced database connected successfully
2025-06-04 03:54:10,052 - root - INFO - Discord API optimizer started
2025-06-04 03:54:10,052 - root - INFO - Bot Status: Performance optimization systems initialized
2025-06-04 03:54:10,052 - root - INFO - Loading bot data from MongoDB...
2025-06-04 03:54:10,052 - root - INFO - Loaded gang data: 5 gangs, 3 leaders, 1 invitations
2025-06-04 03:54:10,052 - root - INFO - Loaded applications: 2 forms, 5 status entries
2025-06-04 03:54:10,052 - root - INFO - Bot data loaded successfully from MongoDB
2025-06-04 03:54:10,052 - root - INFO - Bot Status: Bot initialization completed!
2025-06-04 03:54:11,572 - root - ERROR - Error in data saving: Database connection timeout
NoneType: None
