"""
Comprehensive Performance Manager for Discord Bot
Handles high-load scenarios up to 100,000 concurrent users
"""

import asyncio
import time
import logging
import gc
import psutil
import threading
import weakref
from collections import defaultdict, deque
from typing import Dict, Any, Optional, Callable, List
from dataclasses import dataclass
from datetime import datetime, timedelta
import json

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger('performance_manager')

@dataclass
class PerformanceMetrics:
    """Performance metrics tracking"""
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    avg_response_time: float = 0.0
    peak_memory_usage: float = 0.0
    cache_hit_rate: float = 0.0
    active_connections: int = 0
    queue_size: int = 0
    last_updated: datetime = None

class CircuitBreaker:
    """Circuit breaker pattern for external dependencies"""
    
    def __init__(self, failure_threshold: int = 5, recovery_timeout: int = 60):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.failure_count = 0
        self.last_failure_time = 0
        self.state = 'closed'  # closed, open, half-open
        self.lock = threading.Lock()
    
    def call(self, func: Callable, *args, **kwargs):
        """Execute function with circuit breaker protection"""
        with self.lock:
            if self.state == 'open':
                if time.time() - self.last_failure_time > self.recovery_timeout:
                    self.state = 'half-open'
                else:
                    raise Exception("Circuit breaker is open")
            
            try:
                result = func(*args, **kwargs)
                if self.state == 'half-open':
                    self.state = 'closed'
                    self.failure_count = 0
                return result
            except Exception as e:
                self.failure_count += 1
                self.last_failure_time = time.time()
                
                if self.failure_count >= self.failure_threshold:
                    self.state = 'open'
                
                raise e

class RateLimiter:
    """Advanced rate limiter with per-user and per-guild limits"""
    
    def __init__(self):
        self.user_limits = defaultdict(lambda: deque())
        self.guild_limits = defaultdict(lambda: deque())
        self.global_limit = deque()
        self.lock = threading.Lock()
        
        # Rate limit configurations
        self.user_rate_limit = 10  # requests per minute
        self.guild_rate_limit = 100  # requests per minute
        self.global_rate_limit = 10000  # requests per minute
        self.window_size = 60  # seconds
    
    def is_allowed(self, user_id: int, guild_id: int = None) -> bool:
        """Check if request is allowed based on rate limits"""
        current_time = time.time()
        
        with self.lock:
            # Clean old entries
            self._clean_old_entries(current_time)
            
            # Check global limit
            if len(self.global_limit) >= self.global_rate_limit:
                return False
            
            # Check user limit
            user_requests = self.user_limits[user_id]
            if len(user_requests) >= self.user_rate_limit:
                return False
            
            # Check guild limit if provided
            if guild_id:
                guild_requests = self.guild_limits[guild_id]
                if len(guild_requests) >= self.guild_rate_limit:
                    return False
                guild_requests.append(current_time)
            
            # Add to limits
            user_requests.append(current_time)
            self.global_limit.append(current_time)
            
            return True
    
    def _clean_old_entries(self, current_time: float):
        """Remove entries older than window size"""
        cutoff_time = current_time - self.window_size
        
        # Clean global limit
        while self.global_limit and self.global_limit[0] < cutoff_time:
            self.global_limit.popleft()
        
        # Clean user limits
        for user_id in list(self.user_limits.keys()):
            user_queue = self.user_limits[user_id]
            while user_queue and user_queue[0] < cutoff_time:
                user_queue.popleft()
            if not user_queue:
                del self.user_limits[user_id]
        
        # Clean guild limits
        for guild_id in list(self.guild_limits.keys()):
            guild_queue = self.guild_limits[guild_id]
            while guild_queue and guild_queue[0] < cutoff_time:
                guild_queue.popleft()
            if not guild_queue:
                del self.guild_limits[guild_id]

class OperationQueue:
    """High-performance queue for heavy operations"""
    
    def __init__(self, max_workers: int = 50, max_queue_size: int = 10000):
        self.max_workers = max_workers
        self.max_queue_size = max_queue_size
        self.queue = asyncio.Queue(maxsize=max_queue_size)
        self.workers = []
        self.running = False
        self.stats = {
            'processed': 0,
            'failed': 0,
            'queue_size': 0,
            'active_workers': 0
        }
    
    async def start(self):
        """Start the queue workers"""
        if self.running:
            return
        
        self.running = True
        for i in range(self.max_workers):
            worker = asyncio.create_task(self._worker(f"worker-{i}"))
            self.workers.append(worker)
        
        logger.info(f"Started operation queue with {self.max_workers} workers")
    
    async def stop(self):
        """Stop the queue workers"""
        self.running = False
        
        # Cancel all workers
        for worker in self.workers:
            worker.cancel()
        
        # Wait for workers to finish
        await asyncio.gather(*self.workers, return_exceptions=True)
        self.workers.clear()
        
        logger.info("Stopped operation queue")
    
    async def add_operation(self, operation: Callable, *args, **kwargs) -> bool:
        """Add operation to queue"""
        try:
            if self.queue.qsize() >= self.max_queue_size:
                logger.warning("Operation queue is full, dropping operation")
                return False
            
            await self.queue.put((operation, args, kwargs))
            self.stats['queue_size'] = self.queue.qsize()
            return True
        except Exception as e:
            logger.error(f"Error adding operation to queue: {e}")
            return False
    
    async def _worker(self, worker_name: str):
        """Worker coroutine to process operations"""
        logger.info(f"Started worker: {worker_name}")
        
        while self.running:
            try:
                # Get operation from queue with timeout
                operation, args, kwargs = await asyncio.wait_for(
                    self.queue.get(), timeout=1.0
                )
                
                self.stats['active_workers'] += 1
                
                # Execute operation
                try:
                    if asyncio.iscoroutinefunction(operation):
                        await operation(*args, **kwargs)
                    else:
                        operation(*args, **kwargs)
                    
                    self.stats['processed'] += 1
                except Exception as e:
                    logger.error(f"Error executing operation in {worker_name}: {e}")
                    self.stats['failed'] += 1
                finally:
                    self.stats['active_workers'] -= 1
                    self.stats['queue_size'] = self.queue.qsize()
                    self.queue.task_done()
                    
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                logger.error(f"Error in worker {worker_name}: {e}")
        
        logger.info(f"Stopped worker: {worker_name}")

class PerformanceManager:
    """Main performance manager for the Discord bot"""
    
    _instance = None
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(PerformanceManager, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        if self._initialized:
            return
        
        # Core components
        self.rate_limiter = RateLimiter()
        self.circuit_breaker = CircuitBreaker()
        self.operation_queue = OperationQueue()
        
        # Performance monitoring
        self.metrics = PerformanceMetrics()
        self.response_times = deque(maxlen=1000)  # Last 1000 response times
        self.memory_usage_history = deque(maxlen=100)  # Last 100 memory readings
        
        # Health monitoring
        self.health_checks = {}
        self.last_health_check = 0
        self.health_check_interval = 30  # seconds
        
        # Memory management
        self.memory_threshold = 2 * 1024 * 1024 * 1024  # 2GB threshold
        self.last_gc_time = time.time()
        self.gc_interval = 300  # 5 minutes
        
        # Background tasks
        self.monitoring_task = None
        self.running = False
        
        self._initialized = True
    
    async def start(self):
        """Start the performance manager"""
        if self.running:
            return
        
        self.running = True
        await self.operation_queue.start()
        self.monitoring_task = asyncio.create_task(self._monitoring_loop())
        
        logger.info("Performance manager started")
    
    async def stop(self):
        """Stop the performance manager"""
        self.running = False

        if self.monitoring_task:
            self.monitoring_task.cancel()

        await self.operation_queue.stop()

        logger.info("Performance manager stopped")

    async def _monitoring_loop(self):
        """Background monitoring loop"""
        while self.running:
            try:
                await self._update_metrics()
                await self._check_memory_usage()
                await self._run_health_checks()
                await asyncio.sleep(10)  # Update every 10 seconds
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")

    async def _update_metrics(self):
        """Update performance metrics"""
        try:
            # Update memory usage
            process = psutil.Process()
            memory_info = process.memory_info()
            current_memory = memory_info.rss / 1024 / 1024  # MB

            self.memory_usage_history.append(current_memory)
            self.metrics.peak_memory_usage = max(self.memory_usage_history)

            # Update response time average
            if self.response_times:
                self.metrics.avg_response_time = sum(self.response_times) / len(self.response_times)

            # Update queue size
            self.metrics.queue_size = self.operation_queue.stats['queue_size']

            # Update timestamp
            self.metrics.last_updated = datetime.now()

        except Exception as e:
            logger.error(f"Error updating metrics: {e}")

    async def _check_memory_usage(self):
        """Check memory usage and trigger GC if needed"""
        try:
            current_time = time.time()

            # Get current memory usage
            process = psutil.Process()
            memory_info = process.memory_info()
            current_memory = memory_info.rss

            # Force GC if memory usage is high or interval passed
            if (current_memory > self.memory_threshold or
                current_time - self.last_gc_time > self.gc_interval):

                collected = gc.collect()
                self.last_gc_time = current_time

                # Log memory cleanup
                new_memory = psutil.Process().memory_info().rss
                freed_mb = (current_memory - new_memory) / 1024 / 1024

                logger.info(f"Memory cleanup: {collected} objects collected, "
                           f"{freed_mb:.2f}MB freed")

        except Exception as e:
            logger.error(f"Error checking memory usage: {e}")

    async def _run_health_checks(self):
        """Run registered health checks"""
        try:
            current_time = time.time()

            if current_time - self.last_health_check < self.health_check_interval:
                return

            self.last_health_check = current_time

            for name, check_func in self.health_checks.items():
                try:
                    if asyncio.iscoroutinefunction(check_func):
                        result = await check_func()
                    else:
                        result = check_func()

                    if not result:
                        logger.warning(f"Health check failed: {name}")

                except Exception as e:
                    logger.error(f"Error running health check {name}: {e}")

        except Exception as e:
            logger.error(f"Error running health checks: {e}")

    def register_health_check(self, name: str, check_func: Callable):
        """Register a health check function"""
        self.health_checks[name] = check_func
        logger.info(f"Registered health check: {name}")

    def record_response_time(self, response_time: float):
        """Record a response time for metrics"""
        self.response_times.append(response_time)
        self.metrics.total_requests += 1

    def record_success(self):
        """Record a successful operation"""
        self.metrics.successful_requests += 1

    def record_failure(self):
        """Record a failed operation"""
        self.metrics.failed_requests += 1

    async def execute_with_monitoring(self, operation: Callable, *args, **kwargs):
        """Execute operation with performance monitoring"""
        start_time = time.time()

        try:
            if asyncio.iscoroutinefunction(operation):
                result = await operation(*args, **kwargs)
            else:
                result = operation(*args, **kwargs)

            self.record_success()
            return result

        except Exception as e:
            self.record_failure()
            raise e
        finally:
            response_time = time.time() - start_time
            self.record_response_time(response_time)

    def get_metrics(self) -> Dict[str, Any]:
        """Get current performance metrics"""
        return {
            'total_requests': self.metrics.total_requests,
            'successful_requests': self.metrics.successful_requests,
            'failed_requests': self.metrics.failed_requests,
            'success_rate': (self.metrics.successful_requests / max(self.metrics.total_requests, 1)) * 100,
            'avg_response_time': self.metrics.avg_response_time,
            'peak_memory_usage_mb': self.metrics.peak_memory_usage,
            'current_memory_mb': self.memory_usage_history[-1] if self.memory_usage_history else 0,
            'queue_size': self.metrics.queue_size,
            'active_workers': self.operation_queue.stats['active_workers'],
            'processed_operations': self.operation_queue.stats['processed'],
            'failed_operations': self.operation_queue.stats['failed'],
            'last_updated': self.metrics.last_updated.isoformat() if self.metrics.last_updated else None
        }

# Global performance manager instance
performance_manager = PerformanceManager()

def get_performance_manager():
    """Get the global performance manager instance"""
    return performance_manager
