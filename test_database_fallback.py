#!/usr/bin/env python3
"""
Test the database fallback functionality
"""

import asyncio
import logging

# Set up logging to see what's happening
logging.basicConfig(level=logging.INFO, format='%(levelname)s:%(name)s:%(message)s')

async def test_database_fallback():
    """Test that the optimized database functions fall back gracefully"""
    print("🧪 Testing Database Fallback Functionality...")
    print("=" * 50)
    
    try:
        # Import the optimized functions
        from bot import optimized_load_data, optimized_save_data, PERFORMANCE_OPTIMIZATIONS_ENABLED
        
        print(f"Performance optimizations enabled: {PERFORMANCE_OPTIMIZATIONS_ENABLED}")
        
        # Test 1: Load data (should fall back gracefully if no data exists)
        print("\n1. Testing optimized_load_data...")
        try:
            result = await optimized_load_data()
            print(f"   ✅ Load operation completed: {result}")
        except Exception as e:
            print(f"   ❌ Load operation failed: {e}")
            return False
        
        # Test 2: Save data (should fall back gracefully if enhanced DB not available)
        print("\n2. Testing optimized_save_data...")
        try:
            # Set some dummy global variables for testing
            import bot
            bot.gang_roles = {"test_gang": {"role_id": 123}}
            bot.gang_strikes = {}
            bot.gang_invitations = {}
            bot.applications_status = {}
            bot.application_forms = {}
            bot.application_channel = None
            bot.application_log_channel = None
            bot.reaction_roles = {}
            bot.sticky_messages = {}
            bot.welcome_channel_id = None
            bot.welcome_message = "Welcome!"
            bot.welcome_image_url = None
            bot.tebex_channel = None
            bot.webhook_url = None
            bot.join_role_id = None
            bot.notification_channel_id = None
            
            result = await optimized_save_data()
            print(f"   ✅ Save operation completed: {result}")
        except Exception as e:
            print(f"   ❌ Save operation failed: {e}")
            return False
        
        # Test 3: Test enhanced database manager directly
        if PERFORMANCE_OPTIMIZATIONS_ENABLED:
            print("\n3. Testing enhanced database manager...")
            try:
                from enhanced_database import get_enhanced_db_manager
                enhanced_db = get_enhanced_db_manager()
                
                # Test find_one_cached with non-existent collection
                result = await enhanced_db.find_one_cached("nonexistent_collection", {"test": "query"})
                print(f"   ✅ Non-existent collection handled gracefully: {result}")
                
                # Test update with non-existent collection
                success = await enhanced_db.update_one_with_cache_invalidation(
                    "test_collection", 
                    {"_id": "test"}, 
                    {"$set": {"data": "test"}}, 
                    upsert=True
                )
                print(f"   ✅ Update with collection creation: {success}")
                
            except Exception as e:
                print(f"   ⚠️ Enhanced database test failed (this is expected if MongoDB is not running): {e}")
        
        print("\n" + "=" * 50)
        print("🎉 Database fallback tests completed successfully!")
        print("✅ The bot will gracefully handle missing collections and database issues")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function"""
    print("🔧 Database Fallback Test Suite")
    print("Testing graceful degradation when collections don't exist...")
    print()
    
    try:
        success = await test_database_fallback()
        
        if success:
            print("\n🎯 Result: DATABASE FALLBACK WORKING CORRECTLY")
            print("✅ Bot will start successfully even with missing collections")
        else:
            print("\n❌ Result: DATABASE FALLBACK NEEDS ATTENTION")
            
    except Exception as e:
        print(f"\n💥 Test suite failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
