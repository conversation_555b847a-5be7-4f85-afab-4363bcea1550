"""
Discord API Optimizer for High-Load Scenarios
Handles rate limiting, bulk operations, and API optimization
"""

import asyncio
import time
import logging
import discord
from collections import deque, defaultdict
from typing import List, Dict, Any, Optional, Callable
from dataclasses import dataclass
from datetime import datetime, timedelta
import json

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger('discord_api_optimizer')

@dataclass
class RateLimitBucket:
    """Rate limit bucket for Discord API endpoints"""
    limit: int
    remaining: int
    reset_time: float
    endpoint: str

class DiscordRateLimiter:
    """Advanced Discord API rate limiter with exponential backoff"""
    
    def __init__(self):
        self.buckets = {}
        self.global_rate_limit = False
        self.global_reset_time = 0
        self.lock = asyncio.Lock()
        
        # Rate limit tracking
        self.requests_made = 0
        self.requests_blocked = 0
        self.total_wait_time = 0
    
    async def wait_for_rate_limit(self, endpoint: str) -> float:
        """Wait for rate limit if necessary, returns wait time"""
        async with self.lock:
            wait_time = 0
            
            # Check global rate limit
            if self.global_rate_limit:
                current_time = time.time()
                if current_time < self.global_reset_time:
                    wait_time = self.global_reset_time - current_time
                    logger.warning(f"Global rate limit hit, waiting {wait_time:.2f}s")
                    await asyncio.sleep(wait_time)
                    self.total_wait_time += wait_time
                else:
                    self.global_rate_limit = False
            
            # Check endpoint-specific rate limit
            if endpoint in self.buckets:
                bucket = self.buckets[endpoint]
                current_time = time.time()
                
                if bucket.remaining <= 0 and current_time < bucket.reset_time:
                    endpoint_wait = bucket.reset_time - current_time
                    logger.warning(f"Rate limit hit for {endpoint}, waiting {endpoint_wait:.2f}s")
                    await asyncio.sleep(endpoint_wait)
                    wait_time += endpoint_wait
                    self.total_wait_time += endpoint_wait
                    self.requests_blocked += 1
                elif current_time >= bucket.reset_time:
                    # Reset bucket
                    bucket.remaining = bucket.limit
            
            self.requests_made += 1
            return wait_time
    
    def update_rate_limit(self, endpoint: str, headers: Dict[str, str]):
        """Update rate limit information from response headers"""
        try:
            if 'x-ratelimit-limit' in headers:
                limit = int(headers['x-ratelimit-limit'])
                remaining = int(headers.get('x-ratelimit-remaining', 0))
                reset_after = float(headers.get('x-ratelimit-reset-after', 0))
                
                reset_time = time.time() + reset_after
                
                self.buckets[endpoint] = RateLimitBucket(
                    limit=limit,
                    remaining=remaining,
                    reset_time=reset_time,
                    endpoint=endpoint
                )
            
            # Check for global rate limit
            if headers.get('x-ratelimit-global') == 'true':
                self.global_rate_limit = True
                retry_after = float(headers.get('retry-after', 1))
                self.global_reset_time = time.time() + retry_after
                
        except (ValueError, KeyError) as e:
            logger.warning(f"Error parsing rate limit headers: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get rate limiter statistics"""
        return {
            'requests_made': self.requests_made,
            'requests_blocked': self.requests_blocked,
            'total_wait_time': self.total_wait_time,
            'active_buckets': len(self.buckets),
            'global_rate_limited': self.global_rate_limit
        }

class BulkOperationManager:
    """Manager for bulk Discord operations to reduce API calls"""
    
    def __init__(self, max_batch_size: int = 100, batch_timeout: float = 5.0):
        self.max_batch_size = max_batch_size
        self.batch_timeout = batch_timeout
        
        # Operation queues
        self.message_queues = defaultdict(list)  # channel_id -> messages
        self.role_queues = defaultdict(list)     # guild_id -> role operations
        self.member_queues = defaultdict(list)   # guild_id -> member operations
        
        # Batch timers
        self.batch_timers = {}
        
        # Statistics
        self.operations_batched = 0
        self.api_calls_saved = 0
    
    async def queue_message_send(self, channel_id: int, embed: discord.Embed, 
                                content: str = None) -> bool:
        """Queue message for bulk sending"""
        try:
            message_data = {
                'channel_id': channel_id,
                'embed': embed,
                'content': content,
                'timestamp': time.time()
            }
            
            self.message_queues[channel_id].append(message_data)
            self.operations_batched += 1
            
            # Start batch timer if not already running
            if channel_id not in self.batch_timers:
                self.batch_timers[channel_id] = asyncio.create_task(
                    self._process_message_batch(channel_id)
                )
            
            # Process immediately if batch is full
            if len(self.message_queues[channel_id]) >= self.max_batch_size:
                await self._flush_message_batch(channel_id)
            
            return True
            
        except Exception as e:
            logger.error(f"Error queuing message: {e}")
            return False
    
    async def _process_message_batch(self, channel_id: int):
        """Process message batch after timeout"""
        try:
            await asyncio.sleep(self.batch_timeout)
            await self._flush_message_batch(channel_id)
        except asyncio.CancelledError:
            pass
        except Exception as e:
            logger.error(f"Error processing message batch: {e}")
    
    async def _flush_message_batch(self, channel_id: int):
        """Flush message batch to Discord"""
        try:
            if channel_id not in self.message_queues:
                return
            
            messages = self.message_queues[channel_id].copy()
            self.message_queues[channel_id].clear()
            
            # Cancel timer
            if channel_id in self.batch_timers:
                self.batch_timers[channel_id].cancel()
                del self.batch_timers[channel_id]
            
            if not messages:
                return
            
            # Get channel
            from bot_instance import bot
            channel = bot.get_channel(channel_id)
            if not channel:
                logger.warning(f"Channel {channel_id} not found for batch send")
                return
            
            # Send messages with rate limiting
            rate_limiter = DiscordRateLimiter()
            
            for message_data in messages:
                try:
                    await rate_limiter.wait_for_rate_limit(f"channel_{channel_id}")
                    
                    if message_data['embed']:
                        await channel.send(
                            content=message_data['content'],
                            embed=message_data['embed']
                        )
                    else:
                        await channel.send(content=message_data['content'])
                    
                    self.api_calls_saved += 1
                    
                except Exception as e:
                    logger.error(f"Error sending batched message: {e}")
            
            logger.info(f"Sent batch of {len(messages)} messages to channel {channel_id}")
            
        except Exception as e:
            logger.error(f"Error flushing message batch: {e}")
    
    async def queue_role_operation(self, guild_id: int, user_id: int, 
                                  role_id: int, operation: str) -> bool:
        """Queue role operation for bulk processing"""
        try:
            role_data = {
                'guild_id': guild_id,
                'user_id': user_id,
                'role_id': role_id,
                'operation': operation,  # 'add' or 'remove'
                'timestamp': time.time()
            }
            
            self.role_queues[guild_id].append(role_data)
            self.operations_batched += 1
            
            # Process immediately if batch is full
            if len(self.role_queues[guild_id]) >= self.max_batch_size:
                await self._flush_role_batch(guild_id)
            
            return True
            
        except Exception as e:
            logger.error(f"Error queuing role operation: {e}")
            return False
    
    async def _flush_role_batch(self, guild_id: int):
        """Flush role operations batch"""
        try:
            if guild_id not in self.role_queues:
                return
            
            operations = self.role_queues[guild_id].copy()
            self.role_queues[guild_id].clear()
            
            if not operations:
                return
            
            # Get guild
            from bot_instance import bot
            guild = bot.get_guild(guild_id)
            if not guild:
                logger.warning(f"Guild {guild_id} not found for role batch")
                return
            
            # Group operations by user
            user_operations = defaultdict(list)
            for op in operations:
                user_operations[op['user_id']].append(op)
            
            # Process role operations
            rate_limiter = DiscordRateLimiter()
            
            for user_id, user_ops in user_operations.items():
                try:
                    member = guild.get_member(user_id)
                    if not member:
                        continue
                    
                    roles_to_add = []
                    roles_to_remove = []
                    
                    for op in user_ops:
                        role = guild.get_role(op['role_id'])
                        if role:
                            if op['operation'] == 'add':
                                roles_to_add.append(role)
                            elif op['operation'] == 'remove':
                                roles_to_remove.append(role)
                    
                    # Apply role changes in bulk
                    await rate_limiter.wait_for_rate_limit(f"guild_{guild_id}_roles")
                    
                    if roles_to_add:
                        await member.add_roles(*roles_to_add, reason="Bulk role operation")
                    
                    if roles_to_remove:
                        await member.remove_roles(*roles_to_remove, reason="Bulk role operation")
                    
                    self.api_calls_saved += len(user_ops) - 1  # Saved API calls
                    
                except Exception as e:
                    logger.error(f"Error applying role operations for user {user_id}: {e}")
            
            logger.info(f"Processed batch of {len(operations)} role operations for guild {guild_id}")
            
        except Exception as e:
            logger.error(f"Error flushing role batch: {e}")
    
    async def flush_all_batches(self):
        """Flush all pending batches"""
        try:
            # Flush message batches
            for channel_id in list(self.message_queues.keys()):
                await self._flush_message_batch(channel_id)
            
            # Flush role batches
            for guild_id in list(self.role_queues.keys()):
                await self._flush_role_batch(guild_id)
            
            logger.info("Flushed all pending batches")
            
        except Exception as e:
            logger.error(f"Error flushing all batches: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get bulk operation statistics"""
        return {
            'operations_batched': self.operations_batched,
            'api_calls_saved': self.api_calls_saved,
            'pending_message_batches': len(self.message_queues),
            'pending_role_batches': len(self.role_queues),
            'active_timers': len(self.batch_timers)
        }

class DiscordAPIOptimizer:
    """Main Discord API optimizer"""
    
    _instance = None
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(DiscordAPIOptimizer, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        if self._initialized:
            return
        
        self.rate_limiter = DiscordRateLimiter()
        self.bulk_manager = BulkOperationManager()
        
        # Background tasks
        self.flush_task = None
        self.running = False

        self._initialized = True

    async def start(self):
        """Start the Discord API optimizer"""
        if self.running:
            return

        self.running = True

        # Start periodic flush task
        self.flush_task = asyncio.create_task(self._periodic_flush())

        logger.info("Discord API optimizer started")

    async def stop(self):
        """Stop the Discord API optimizer"""
        self.running = False

        if self.flush_task:
            self.flush_task.cancel()

        # Flush all pending operations
        await self.bulk_manager.flush_all_batches()

        logger.info("Discord API optimizer stopped")

    async def _periodic_flush(self):
        """Periodically flush pending operations"""
        while self.running:
            try:
                await asyncio.sleep(30)  # Flush every 30 seconds
                await self.bulk_manager.flush_all_batches()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in periodic flush: {e}")

    async def send_optimized_message(self, channel_id: int, embed: discord.Embed = None,
                                   content: str = None, force_immediate: bool = False) -> bool:
        """Send message with optimization"""
        if force_immediate:
            # Send immediately with rate limiting
            try:
                from bot_instance import bot
                channel = bot.get_channel(channel_id)
                if not channel:
                    return False

                await self.rate_limiter.wait_for_rate_limit(f"channel_{channel_id}")

                if embed:
                    await channel.send(content=content, embed=embed)
                else:
                    await channel.send(content=content)

                return True
            except Exception as e:
                logger.error(f"Error sending immediate message: {e}")
                return False
        else:
            # Queue for bulk sending
            return await self.bulk_manager.queue_message_send(channel_id, embed, content)

    async def add_role_optimized(self, guild_id: int, user_id: int, role_id: int,
                               force_immediate: bool = False) -> bool:
        """Add role with optimization"""
        if force_immediate:
            try:
                from bot_instance import bot
                guild = bot.get_guild(guild_id)
                if not guild:
                    return False

                member = guild.get_member(user_id)
                role = guild.get_role(role_id)

                if not member or not role:
                    return False

                await self.rate_limiter.wait_for_rate_limit(f"guild_{guild_id}_roles")
                await member.add_roles(role, reason="Optimized role addition")

                return True
            except Exception as e:
                logger.error(f"Error adding role immediately: {e}")
                return False
        else:
            return await self.bulk_manager.queue_role_operation(guild_id, user_id, role_id, 'add')

    async def remove_role_optimized(self, guild_id: int, user_id: int, role_id: int,
                                  force_immediate: bool = False) -> bool:
        """Remove role with optimization"""
        if force_immediate:
            try:
                from bot_instance import bot
                guild = bot.get_guild(guild_id)
                if not guild:
                    return False

                member = guild.get_member(user_id)
                role = guild.get_role(role_id)

                if not member or not role:
                    return False

                await self.rate_limiter.wait_for_rate_limit(f"guild_{guild_id}_roles")
                await member.remove_roles(role, reason="Optimized role removal")

                return True
            except Exception as e:
                logger.error(f"Error removing role immediately: {e}")
                return False
        else:
            return await self.bulk_manager.queue_role_operation(guild_id, user_id, role_id, 'remove')

    def get_comprehensive_stats(self) -> Dict[str, Any]:
        """Get comprehensive optimization statistics"""
        return {
            'rate_limiter': self.rate_limiter.get_stats(),
            'bulk_manager': self.bulk_manager.get_stats(),
            'is_running': self.running
        }

# Global Discord API optimizer instance
discord_api_optimizer = DiscordAPIOptimizer()

def get_discord_api_optimizer():
    """Get the global Discord API optimizer instance"""
    return discord_api_optimizer
