import asyncio
import logging
import gc
import time
import weakref
from datetime import datetime, timedelta
from typing import Dict, Any, List, Set, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('memory_manager')

class MemoryManager:
    """
    Memory management system for the bot to handle cleanup of old data
    and implement garbage collection strategies.
    """
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(MemoryManager, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if self._initialized:
            return
            
        # Cache settings
        self.caches = {}
        self.cache_ttls = {}
        self.cache_max_sizes = {}
        self.cache_access_times = {}
        
        # Cleanup settings
        self.cleanup_interval = 300  # 5 minutes
        self.cleanup_task = None
        self.running = False
        
        # Memory monitoring
        self.last_gc_time = time.time()
        self.gc_interval = 600  # 10 minutes
        self.memory_threshold = 500 * 1024 * 1024  # 500 MB
        
        # Weak references to large objects
        self.tracked_objects = weakref.WeakSet()
        
        self._initialized = True
        
    def start(self):
        """Start the memory manager background tasks"""
        if not self.running:
            self.running = True
            self.cleanup_task = asyncio.create_task(self._cleanup_loop())
            logger.info("Memory manager started")
            
    def stop(self):
        """Stop the memory manager background tasks"""
        self.running = False
        if self.cleanup_task:
            self.cleanup_task.cancel()
            self.cleanup_task = None
        logger.info("Memory manager stopped")
        
    async def _cleanup_loop(self):
        """Background task to periodically clean up expired cache items and run garbage collection"""
        try:
            while self.running:
                # Clean up expired cache items
                self._cleanup_caches()
                
                # Run garbage collection if needed
                self._check_memory_usage()
                
                # Wait for next cleanup interval
                await asyncio.sleep(self.cleanup_interval)
        except asyncio.CancelledError:
            logger.info("Cleanup task cancelled")
        except Exception as e:
            logger.error(f"Error in cleanup loop: {e}")
            
    def _cleanup_caches(self):
        """Clean up expired cache items"""
        try:
            current_time = time.time()
            items_removed = 0
            
            # Check each cache
            for cache_name, cache in self.caches.items():
                # Skip if cache doesn't exist
                if cache_name not in self.cache_ttls:
                    continue
                    
                # Find expired items
                expired_keys = []
                for key, expiry_time in self.cache_ttls[cache_name].items():
                    if expiry_time <= current_time:
                        expired_keys.append(key)
                
                # Remove expired items
                for key in expired_keys:
                    if key in cache:
                        del cache[key]
                    if key in self.cache_ttls[cache_name]:
                        del self.cache_ttls[cache_name][key]
                    if cache_name in self.cache_access_times and key in self.cache_access_times[cache_name]:
                        del self.cache_access_times[cache_name][key]
                    items_removed += 1
                    
            if items_removed > 0:
                logger.info(f"Cleaned up {items_removed} expired cache items")
        except Exception as e:
            logger.error(f"Error cleaning up caches: {e}")
            
    def _check_memory_usage(self):
        """Check memory usage and run garbage collection if needed"""
        try:
            current_time = time.time()
            
            # Run garbage collection periodically or when memory usage is high
            if current_time - self.last_gc_time >= self.gc_interval:
                # Force garbage collection
                collected = gc.collect()
                self.last_gc_time = current_time
                logger.info(f"Garbage collection completed: {collected} objects collected")
        except Exception as e:
            logger.error(f"Error checking memory usage: {e}")
            
    def register_cache(self, name: str, max_size: int = 1000, default_ttl: int = 300):
        """Register a new cache with the memory manager"""
        if name not in self.caches:
            self.caches[name] = {}
            self.cache_ttls[name] = {}
            self.cache_access_times[name] = {}
            self.cache_max_sizes[name] = max_size
            logger.info(f"Registered new cache: {name} (max_size={max_size}, default_ttl={default_ttl})")
            
    def cache_get(self, cache_name: str, key: str) -> Optional[Any]:
        """Get an item from a cache"""
        if cache_name not in self.caches:
            return None
            
        cache = self.caches[cache_name]
        if key not in cache:
            return None
            
        # Check if item is expired
        if cache_name in self.cache_ttls and key in self.cache_ttls[cache_name]:
            if self.cache_ttls[cache_name][key] <= time.time():
                # Remove expired item
                del cache[key]
                del self.cache_ttls[cache_name][key]
                if key in self.cache_access_times[cache_name]:
                    del self.cache_access_times[cache_name][key]
                return None
                
        # Update access time
        self.cache_access_times[cache_name][key] = time.time()
        
        return cache[key]
        
    def cache_set(self, cache_name: str, key: str, value: Any, ttl: Optional[int] = None):
        """Set an item in a cache with TTL"""
        if cache_name not in self.caches:
            self.register_cache(cache_name)
            
        cache = self.caches[cache_name]
        
        # Check if cache is full
        if len(cache) >= self.cache_max_sizes[cache_name]:
            # Remove least recently used items
            if cache_name in self.cache_access_times:
                access_times = self.cache_access_times[cache_name]
                # Sort by access time and remove oldest 10% of items
                items_to_remove = int(len(access_times) * 0.1) + 1
                oldest_keys = sorted(access_times.items(), key=lambda x: x[1])[:items_to_remove]
                
                for old_key, _ in oldest_keys:
                    if old_key in cache:
                        del cache[old_key]
                    if old_key in self.cache_ttls[cache_name]:
                        del self.cache_ttls[cache_name][old_key]
                    if old_key in access_times:
                        del access_times[old_key]
                        
        # Set new cache item
        cache[key] = value
        
        # Set TTL
        if ttl is not None:
            self.cache_ttls[cache_name][key] = time.time() + ttl
            
        # Set access time
        self.cache_access_times[cache_name][key] = time.time()
        
    def cache_invalidate(self, cache_name: str, key_prefix: Optional[str] = None):
        """Invalidate cache items by prefix or all if None"""
        if cache_name not in self.caches:
            return
            
        cache = self.caches[cache_name]
        
        if key_prefix is None:
            # Clear entire cache
            cache.clear()
            if cache_name in self.cache_ttls:
                self.cache_ttls[cache_name].clear()
            if cache_name in self.cache_access_times:
                self.cache_access_times[cache_name].clear()
        else:
            # Clear items with matching prefix
            keys_to_remove = [k for k in cache if k.startswith(key_prefix)]
            for key in keys_to_remove:
                if key in cache:
                    del cache[key]
                if cache_name in self.cache_ttls and key in self.cache_ttls[cache_name]:
                    del self.cache_ttls[cache_name][key]
                if cache_name in self.cache_access_times and key in self.cache_access_times[cache_name]:
                    del self.cache_access_times[cache_name][key]
                    
    def track_object(self, obj):
        """Track a large object for garbage collection"""
        self.tracked_objects.add(obj)
        
    def untrack_object(self, obj):
        """Stop tracking an object"""
        if obj in self.tracked_objects:
            self.tracked_objects.remove(obj)

    def get_memory_usage(self) -> float:
        """Get current memory usage in MB"""
        try:
            import psutil
            process = psutil.Process()
            memory_info = process.memory_info()
            return memory_info.rss / 1024 / 1024  # Convert to MB
        except ImportError:
            # Fallback if psutil not available
            import sys
            return sys.getsizeof(self.caches) / 1024 / 1024
        except Exception:
            return 0.0

    def get_stats(self) -> Dict[str, Any]:
        """Get comprehensive memory manager statistics"""
        try:
            # Calculate cache statistics
            total_cache_items = sum(len(cache) for cache in self.caches.values())
            total_caches = len(self.caches)

            # Calculate memory usage
            current_memory_mb = self.get_memory_usage()

            # Get garbage collection stats
            gc_stats = gc.get_stats()
            total_gc_collections = sum(stat.get('collections', 0) for stat in gc_stats)

            # Calculate objects cleaned (estimate based on cache cleanups)
            objects_cleaned = 0
            for cache_name in self.caches:
                if cache_name in self.cache_ttls:
                    objects_cleaned += len(self.cache_ttls[cache_name])

            # Peak memory usage (estimate)
            peak_memory_mb = max(current_memory_mb, getattr(self, '_peak_memory', current_memory_mb))
            self._peak_memory = peak_memory_mb

            return {
                'current_memory_mb': current_memory_mb,
                'peak_memory_mb': peak_memory_mb,
                'total_caches': total_caches,
                'total_cache_items': total_cache_items,
                'gc_collections': total_gc_collections,
                'objects_cleaned': objects_cleaned,
                'tracked_objects': len(self.tracked_objects),
                'running': self.running,
                'cleanup_interval': self.cleanup_interval,
                'gc_interval': self.gc_interval,
                'memory_threshold_mb': self.memory_threshold / 1024 / 1024,
                'last_gc_time': datetime.fromtimestamp(self.last_gc_time).strftime('%Y-%m-%d %H:%M:%S'),
                'cache_details': {
                    name: {
                        'items': len(cache),
                        'max_size': self.cache_max_sizes.get(name, 'unlimited'),
                        'ttl_items': len(self.cache_ttls.get(name, {}))
                    }
                    for name, cache in self.caches.items()
                }
            }
        except Exception as e:
            logger.error(f"Error getting memory stats: {e}")
            return {
                'current_memory_mb': 0.0,
                'peak_memory_mb': 0.0,
                'total_caches': len(self.caches),
                'total_cache_items': 0,
                'gc_collections': 0,
                'objects_cleaned': 0,
                'tracked_objects': 0,
                'running': self.running,
                'error': str(e)
            }

# Create global memory manager instance
memory_manager = MemoryManager()

# Start memory manager when imported
def start_memory_manager():
    memory_manager.start()

# Function to get the memory manager instance
def get_memory_manager():
    return memory_manager
