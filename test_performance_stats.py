#!/usr/bin/env python3
"""
Test the performance_stats functionality
"""

import asyncio
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s:%(name)s:%(message)s')

async def test_performance_stats():
    """Test that performance stats can be generated without errors"""
    print("🧪 Testing Performance Stats Generation...")
    print("=" * 50)
    
    try:
        # Import required modules
        from memory_manager import get_memory_manager
        
        print("1. Testing Memory Manager stats...")
        memory_manager = get_memory_manager()
        memory_stats = memory_manager.get_stats()
        print(f"   ✅ Memory stats generated: {len(memory_stats)} metrics")
        print(f"   📊 Current memory: {memory_stats['current_memory_mb']:.1f}MB")
        print(f"   📊 Total caches: {memory_stats['total_caches']}")
        
        # Test performance optimizations if available
        try:
            from performance_manager import get_performance_manager
            from enhanced_database import get_enhanced_db_manager
            from discord_api_optimizer import get_discord_api_optimizer
            
            print("\n2. Testing Performance Manager stats...")
            performance_manager = get_performance_manager()
            await performance_manager.start()
            perf_metrics = performance_manager.get_metrics()
            print(f"   ✅ Performance metrics generated: {len(perf_metrics)} metrics")
            
            print("\n3. Testing Enhanced Database stats...")
            enhanced_db = get_enhanced_db_manager()
            db_stats = enhanced_db.get_performance_stats()
            print(f"   ✅ Database stats generated: {len(db_stats)} categories")
            
            print("\n4. Testing Discord API Optimizer stats...")
            api_optimizer = get_discord_api_optimizer()
            await api_optimizer.start()
            api_stats = api_optimizer.get_comprehensive_stats()
            print(f"   ✅ API stats generated: {len(api_stats)} categories")
            
            # Cleanup
            await performance_manager.stop()
            await api_optimizer.stop()
            
        except ImportError as e:
            print(f"   ⚠️ Performance optimizations not available: {e}")
        
        print("\n" + "=" * 50)
        print("🎉 Performance stats test completed successfully!")
        print("✅ The /performance_stats command should work correctly")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_stats_formatting():
    """Test that stats can be formatted for Discord embed"""
    print("\n🎨 Testing Stats Formatting...")
    print("=" * 30)
    
    try:
        from memory_manager import get_memory_manager
        
        # Get memory stats
        memory_manager = get_memory_manager()
        memory_stats = memory_manager.get_stats()
        
        # Test formatting like in the Discord command
        formatted_stats = f"```Current Usage: {memory_stats['current_memory_mb']:.1f}MB\n" \
                         f"Peak Usage: {memory_stats['peak_memory_mb']:.1f}MB\n" \
                         f"GC Collections: {memory_stats['gc_collections']:,}\n" \
                         f"Objects Cleaned: {memory_stats['objects_cleaned']:,}```"
        
        print("   ✅ Stats formatted successfully for Discord embed")
        print(f"   📝 Sample output:\n{formatted_stats}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Formatting test failed: {e}")
        return False

async def main():
    """Main test function"""
    print("🔧 Performance Stats Test Suite")
    print("Testing the /performance_stats command functionality...")
    print()
    
    try:
        success1 = await test_performance_stats()
        success2 = await test_stats_formatting()
        
        if success1 and success2:
            print("\n🎯 Result: PERFORMANCE STATS WORKING CORRECTLY")
            print("✅ The /performance_stats command is ready to use")
        else:
            print("\n❌ Result: PERFORMANCE STATS NEED ATTENTION")
            
    except Exception as e:
        print(f"\n💥 Test suite failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
