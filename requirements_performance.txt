# Performance Optimization Dependencies for Discord Bot
# Install with: pip install -r requirements_performance.txt

# Core performance monitoring
psutil>=5.9.0

# Enhanced database operations
motor>=3.3.0
pymongo>=4.5.0

# Memory management and optimization
memory-profiler>=0.61.0

# Async utilities
aiofiles>=23.2.1

# Data structures and caching
cachetools>=5.3.0

# Monitoring and metrics
prometheus-client>=0.17.1

# Optional: Advanced profiling (uncomment if needed)
# py-spy>=0.3.14
# line-profiler>=4.1.1

# Optional: Redis for distributed caching (uncomment if using Redis)
# redis>=4.6.0
# aioredis>=2.0.1

# Optional: Advanced database features (uncomment if needed)
# asyncpg>=0.28.0  # For PostgreSQL
# aiomysql>=0.2.0  # For MySQL
