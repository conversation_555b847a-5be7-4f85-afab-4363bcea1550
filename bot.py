import os
import json
import logging
import asyncio
import threading
import random
import time
from datetime import datetime, timezone
import pymongo
import discord
from discord.ext import tasks
from discord import app_commands
from discord.ui import Select, View, Button, Modal, TextInput
from colorama import init
from bot_instance import bot  # Import bot from bot_instance
from tickets import (
    ticket_config, set_staff_role,
    set_transcript_channel, close_ticket, create_ticket, load_ticket_data,
    save_ticket_data, add_category, set_ticket_channel, create_ticket_panel,
    reopen_ticket, claim_ticket, delete_message_with_backoff
)
# Ticket customizer functionality is now integrated into tickets.py
from database import save_data, load_data, save_transaction, get_transactions, get_guild_settings, save_guild_settings
from memory_manager import get_memory_manager
from utils import (
    load_json, save_json, get_file_lock, log_permission_check, setup_logging,
    get_script_directory, console_log, log_command_execution, log_error_to_console, log_bot_status
)

# Import performance optimization modules
try:
    from performance_manager import get_performance_manager
    from enhanced_database import get_enhanced_db_manager
    from discord_api_optimizer import get_discord_api_optimizer
    PERFORMANCE_OPTIMIZATIONS_ENABLED = True
    # Log to file only - no console spam
    logging.info("Performance optimizations loaded successfully")
except ImportError as e:
    PERFORMANCE_OPTIMIZATIONS_ENABLED = False
    # Only log to console if it's a critical issue
    logging.warning(f"Performance optimizations not available: {e}")

# Performance-optimized database operations
async def optimized_save_data():
    """Save data using enhanced database if available, fallback to regular database"""
    try:
        if PERFORMANCE_OPTIMIZATIONS_ENABLED:
            enhanced_db = get_enhanced_db_manager()
            performance_manager = get_performance_manager()

            # Use performance monitoring
            async def save_operation():
                try:
                    # Prepare data structure
                    data = {
                        "gangs": {
                            "roles": gang_roles,
                            "strikes": gang_strikes,
                            "invitations": gang_invitations
                        },
                        "applications": {
                            "status": applications_status,
                            "forms": application_forms,
                            "channel": application_channel,
                            "log_channel": application_log_channel
                        },
                        "reaction_roles": reaction_roles,
                        "sticky_messages": sticky_messages,
                        "welcome": {
                            "channel_id": welcome_channel_id,
                            "message": welcome_message,
                            "image_url": welcome_image_url
                        },
                        "tebex": {
                            "channel": tebex_channel,
                            "webhook_url": webhook_url
                        },
                        "join_role_id": join_role_id,
                        "notification_channel_id": notification_channel_id
                    }

                    # Save to enhanced database with cache invalidation
                    await enhanced_db.update_one_with_cache_invalidation(
                        "settings",
                        {"_id": "bot_data"},
                        {"$set": data},
                        upsert=True
                    )

                    logging.info("Data saved successfully to enhanced database")
                    return True

                except Exception as e:
                    logging.warning(f"Enhanced database save failed: {e}, falling back to regular database")
                    return False

            result = await performance_manager.execute_with_monitoring(save_operation)
            if result:
                return True
            else:
                # Enhanced database save failed, fall back to regular database
                logging.info("Falling back to regular database for data saving")
                return await save_data()
        else:
            # Fallback to regular database
            return await save_data()

    except Exception as e:
        logging.error(f"Error in optimized_save_data: {e}")
        # Fallback to regular database
        return await save_data()

async def optimized_load_data():
    """Load data using enhanced database if available, fallback to regular database"""
    try:
        if PERFORMANCE_OPTIMIZATIONS_ENABLED:
            enhanced_db = get_enhanced_db_manager()
            performance_manager = get_performance_manager()

            # Use performance monitoring and caching
            async def load_operation():
                try:
                    # Load from enhanced database with caching
                    data = await enhanced_db.find_one_cached(
                        "settings",
                        {"_id": "bot_data"},
                        ttl=300  # 5 minute cache
                    )

                    if data:
                        # Update global variables
                        global gang_roles, gang_strikes, gang_invitations
                        global applications_status, application_forms, application_channel, application_log_channel
                        global reaction_roles, sticky_messages
                        global welcome_channel_id, welcome_message, welcome_image_url
                        global tebex_channel, webhook_url, join_role_id, notification_channel_id

                        # Load gang data
                        gangs_data = data.get("gangs", {})
                        gang_roles = gangs_data.get("roles", {})
                        gang_strikes = gangs_data.get("strikes", {})
                        gang_invitations = gangs_data.get("invitations", {})

                        # Load application data
                        app_data = data.get("applications", {})
                        applications_status = app_data.get("status", {})
                        application_forms = app_data.get("forms", {})
                        application_channel = app_data.get("channel")
                        application_log_channel = app_data.get("log_channel")

                        # Load other data
                        reaction_roles = data.get("reaction_roles", {})
                        sticky_messages = data.get("sticky_messages", {})

                        welcome_data = data.get("welcome", {})
                        welcome_channel_id = welcome_data.get("channel_id")
                        welcome_message = welcome_data.get("message", "Welcome!")
                        welcome_image_url = welcome_data.get("image_url")

                        tebex_data = data.get("tebex", {})
                        tebex_channel = tebex_data.get("channel")
                        webhook_url = tebex_data.get("webhook_url")

                        join_role_id = data.get("join_role_id")
                        notification_channel_id = data.get("notification_channel_id")

                        logging.info("Data loaded successfully from enhanced database")
                        return True
                    else:
                        # No data found in enhanced database, this is normal for first run
                        logging.info("No data found in enhanced database, will use regular database")
                        return False

                except Exception as e:
                    logging.warning(f"Enhanced database load failed: {e}, falling back to regular database")
                    return False

            result = await performance_manager.execute_with_monitoring(load_operation)
            if result:
                return True
            else:
                # Enhanced database didn't have data, fall back to regular database
                logging.info("Falling back to regular database for data loading")
                return await load_data()
        else:
            # Fallback to regular database
            return await load_data()

    except Exception as e:
        logging.error(f"Error in optimized_load_data: {e}")
        # Fallback to regular database
        return await load_data()

# Helper function to use optimized save when available
async def save_data_optimized():
    """Use optimized save_data if available, otherwise use regular save_data"""
    if PERFORMANCE_OPTIMIZATIONS_ENABLED:
        return await optimized_save_data()
    else:
        return await save_data()

class RateLimitHandler:
    def __init__(self):
        self.rate_limits = {}
        self.queues = {}
        self.processing = {}
        self.max_retries = 10  # Increased from 3
        self.base_delay = 0.5
        self.max_delay = 15.0
        self.bulk_queue = asyncio.Queue()
        self.bulk_processing = False

    async def execute(self, key, coroutine, *args, **kwargs):
        """Execute a coroutine with enhanced rate limit handling"""
        if key not in self.queues:
            self.queues[key] = asyncio.Queue()
            self.processing[key] = False

        # Add to queue
        await self.queues[key].put((coroutine, args, kwargs))

        # Start processing if not already running
        if not self.processing[key]:
            self.processing[key] = True
            asyncio.create_task(self._process_queue(key))

    async def execute_bulk(self, operations):
        """Handle bulk operations more efficiently"""
        for op in operations:
            await self.bulk_queue.put(op)

        if not self.bulk_processing:
            self.bulk_processing = True
            asyncio.create_task(self._process_bulk_queue())

    async def _process_bulk_queue(self):
        """Process bulk operations with smart rate limiting"""
        try:
            batch_size = 0
            last_operation_time = time.time()

            while not self.bulk_queue.empty():
                current_time = time.time()
                time_diff = current_time - last_operation_time

                # Adjust batch size based on rate limit encounters
                if time_diff > 5:  # Reset batch size after 5 seconds of no rate limits
                    batch_size = min(50, batch_size + 10)

                # Process a batch of operations
                operations = []
                for _ in range(batch_size):
                    if self.bulk_queue.empty():
                        break
                    operations.append(await self.bulk_queue.get())

                try:
                    # Execute batch with retry logic
                    success = await self._execute_with_backoff(operations)
                    if success:
                        last_operation_time = time.time()
                    else:
                        batch_size = max(1, batch_size // 2)  # Reduce batch size on failure

                except Exception as e:
                    print(f"Error in bulk processing: {e}")
                    batch_size = max(1, batch_size // 2)

                # Add small delay between batches
                await asyncio.sleep(0.1)

        finally:
            self.bulk_processing = False

    async def _process_queue(self, key):
        """Process queued items with enhanced rate limiting"""
        try:
            while not self.queues[key].empty():
                # Apply rate limit with jitter
                if key in self.rate_limits:
                    wait_time = self.rate_limits[key] - time.time()
                    if wait_time > 0:
                        jitter = random.uniform(0, 0.1 * wait_time)
                        await asyncio.sleep(wait_time + jitter)

                # Get next item
                coroutine, args, kwargs = await self.queues[key].get()

                try:
                    # Execute with improved retry logic
                    for attempt in range(self.max_retries):
                        try:
                            await coroutine(*args, **kwargs)
                            break
                        except discord.HTTPException as e:
                            if e.status == 429:  # Rate limit
                                retry_after = e.retry_after
                                self.rate_limits[key] = time.time() + retry_after
                                # Calculate exponential backoff with jitter
                                backoff = min(self.max_delay, self.base_delay * (2 ** attempt))
                                jitter = random.uniform(0, 0.1 * backoff)
                                await asyncio.sleep(backoff + jitter)
                                continue
                            raise
                except Exception as e:
                    print(f"Error processing {key}: {e}")

                # Dynamic delay between operations
                await asyncio.sleep(random.uniform(0.1, 0.3))

        finally:
            self.processing[key] = False

    async def _execute_with_backoff(self, operations):
        """Execute operations with exponential backoff"""
        for attempt in range(self.max_retries):
            try:
                for op in operations:
                    await op()
                return True
            except discord.HTTPException as e:
                if e.status == 429:
                    backoff = min(self.max_delay, self.base_delay * (2 ** attempt))
                    jitter = random.uniform(0, 0.1 * backoff)
                    await asyncio.sleep(backoff + jitter)
                    continue
                raise
        return False

# Create global rate limit handler
rate_limiter = RateLimitHandler()

# Initialize colorama
init()

# Initialize file locks
file_locks = {}

# Initialize all global variables at the top level
gang_strikes = {}
gang_roles = {}
gang_members = {}
gang_leaders = {}
gang_invitations = {}  # Store pending gang invitations with confirmation system
applications_status = {}
reaction_roles = {}
reaction_message_id = None
reaction_channel_id = None
application_forms = {}
application_channel = None
application_log_channel = None
sticky_messages = {}
welcome_channel_id = None
welcome_message = "Welcome!"
welcome_image_url = None
vanity_url = None
role_name = None
notification_channel_id = None
join_role_id = None
tebex_channel = None
webhook_url = None

async def delete_message(message):
    """Delete a message with enhanced rate limit handling"""
    try:
        await rate_limiter.execute(
            'delete_message',
            message.delete,
            reason="Bulk operation"
        )
        return True
    except Exception as e:
        print(f"Error deleting message: {e}")
        return False

async def delete_messages_bulk(messages):
    """Delete multiple messages efficiently"""
    try:
        operations = [
            lambda m=msg: m.delete(reason="Bulk operation")
            for msg in messages
        ]
        await rate_limiter.execute_bulk(operations)
        return True
    except Exception as e:
        print(f"Error in bulk message deletion: {e}")
        return False

# Set up dual logging - detailed file logging + clean console output
setup_logging('bot.log', logging.INFO, logging.WARNING)

# Get the directory of the current script
script_dir = get_script_directory()

# Define file paths for data storage
DATA_FILE_PATH = os.path.join(script_dir, 'bot_data.json')
EVENT_LOGS_FILE = os.path.join(script_dir, 'event_logs.json')

# Create empty data files if they don't exist
def ensure_data_files_exist():
    """Create empty data files if they don't exist"""
    try:
        # Create bot_data.json if it doesn't exist
        if not os.path.exists(DATA_FILE_PATH):
            with open(DATA_FILE_PATH, 'w') as f:
                json.dump({}, f)
            logging.info(f"Created empty data file at {DATA_FILE_PATH}")

        # Create event_logs.json if it doesn't exist
        if not os.path.exists(EVENT_LOGS_FILE):
            with open(EVENT_LOGS_FILE, 'w') as f:
                json.dump({}, f)
            logging.info(f"Created empty event logs file at {EVENT_LOGS_FILE}")
    except Exception as e:
        logging.error(f"Error creating data files: {e}")

# Ensure data files exist
ensure_data_files_exist()

# Load configuration
settings_path = os.path.join(script_dir, 'Settings.json')

# Check if the settings file exists
if not os.path.exists(settings_path):
    print(f"Error: Settings file not found at {settings_path}.")
    exit(1)

# Load configuration
with open(settings_path) as f:
    Settings = json.load(f)

# Get the token and prefix
token = Settings.get('Token')
prefix = Settings.get("Prefix")

# Check if the token is loaded correctly
if token is None:
    console_log("Token is not set in the Settings.json file.", "ERROR")
    exit(1)  # Exit the program if the token is not found

# Initialize applications_status
applications_status = {}

# Use the existing command tree associated with the bot
tree = bot.tree


# Utility Functions
async def find_role_by_name(guild, role_name):
    for role in guild.roles:
        if role.name == role_name:
            return role
    return None

async def send_embed(channel, title, description, color=discord.Color.blue()):
    embed = discord.Embed(title=title, description=description, color=color)
    await channel.send(embed=embed)

# Vanity Role System
class Status:
    roles = 0

def safePrint(member=None, action=None, vocab=None, color=None):
    timestamp = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
    if vocab is not None:
        print(f"[{timestamp}] {member} [{color}{action} {vanity_url} {vocab} status]")
    else:
        print(f"[{timestamp}] {member} [{color}{action}]")

@bot.event
async def on_guild_join(guild):
    """Event handler for when the bot joins a new guild"""
    try:
        # Initialize default settings for the new guild
        default_settings = {
            "guild_id": guild.id,
            "vanity": {
                "url": None,
                "role_name": None
            },
            "notification_channel_id": None
        }

        # Save the default settings to database
        await save_guild_settings(guild.id, default_settings)
        print(f"Bot joined new guild: {guild.name} (ID: {guild.id})")
    except Exception as e:
        print(f"Error initializing guild settings: {e}")

@tasks.loop(seconds=10)  # Check every 10 seconds for more frequent updates
async def check_vanity_status():
    """Check vanity status across all guilds the bot is in"""
    for guild in bot.guilds:
        try:
            # Get guild-specific settings
            settings = await get_guild_settings(guild.id)
            if not settings:
                # Initialize guild settings if they don't exist
                settings = {
                    "guild_id": guild.id,
                    "vanity": {},
                    "notification_channel_id": None
                }
                await save_guild_settings(guild.id, settings)
                continue

            # Check if vanity settings exist
            vanity_settings = settings.get("vanity", {})
            if not vanity_settings.get("url") or not vanity_settings.get("role_name"):
                continue

            vanity_url = vanity_settings["url"]
            role_name = vanity_settings["role_name"]
            notification_channel_id = settings.get("notification_channel_id")

            # Find the role
            role = await find_role_by_name(guild, role_name)
            if not role:
                print(f"Role '{role_name}' not found in guild {guild.name}")
                continue

            # Get notification channel
            notification_channel = guild.get_channel(notification_channel_id) if notification_channel_id else None

            for member in guild.members:
                if member.bot:
                    continue
                # Check if the member is online, idle, or do not disturb
                if member.status in (discord.Status.online, discord.Status.idle, discord.Status.dnd):
                    has_vanity_status = any(activity.name == vanity_url for activity in member.activities if isinstance(activity, discord.CustomActivity))

                    if has_vanity_status and role not in member.roles:
                        await member.add_roles(role)
                        if notification_channel:
                            # Get custom add role title and message
                            add_role_title = vanity_settings.get("add_role_title", "Priority Queue Granted")
                            add_role_message = vanity_settings.get("add_role_message", "User just added '{status}' as their custom Discord status and received free priority queue!")

                            # Format the message with member and status
                            formatted_message = add_role_message.format(
                                member=member.mention,
                                status=vanity_url
                            )

                            embed = discord.Embed(
                                title=add_role_title,
                                description=formatted_message,
                                color=discord.Color.green()
                            )
                            await notification_channel.send(embed=embed)
                        safePrint(member, "Added role", "to", Fore.GREEN)
                    elif not has_vanity_status and role in member.roles:
                        await member.remove_roles(role)
                        if notification_channel:
                            # Get custom remove role title and message
                            remove_role_title = vanity_settings.get("remove_role_title", "Priority Queue Removed")
                            remove_role_message = vanity_settings.get("remove_role_message", "User has removed '{status}' from their custom Discord status")

                            # Format the message with member and status
                            formatted_message = remove_role_message.format(
                                member=member.mention,
                                status=vanity_url
                            )

                            embed = discord.Embed(
                                title=remove_role_title,
                                description=formatted_message,
                                color=discord.Color.red()
                            )
                            await notification_channel.send(embed=embed)
                        safePrint(member, "Removed role", "from", Fore.RED)
        except Exception as e:
            print(f"Error checking vanity status in guild {guild.name}: {e}")

@tree.command(name="setup_tickets", description="Setup the ticket system for the server")
@app_commands.default_permissions(administrator=True)
async def setup_tickets_slash(interaction: discord.Interaction):
    try:
        await interaction.response.defer(ephemeral=True)

        # Import the setup_ticket_system function from tickets.py
        from tickets import setup_ticket_system

        # Call the setup_ticket_system function with the interaction
        await setup_ticket_system(interaction)

    except Exception as e:
        logging.error(f"Error in setup_tickets_slash: {e}")
        await interaction.followup.send("An error occurred while setting up the ticket system.", ephemeral=True)
        return

# The customize_ticket command is now in ticket_customizer.py

@tree.command(name="set_vanity", description="Set the vanity status and role")
@app_commands.default_permissions(administrator=True)
async def set_vanity(interaction: discord.Interaction, status: str, role: discord.Role):
    if not log_permission_check(interaction, "set_vanity"):
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        return
    try:
        # Defer the response to prevent timeout
        await interaction.response.defer(ephemeral=True)

        # Get or create guild settings
        settings = await get_guild_settings(interaction.guild_id)
        if not settings:
            settings = {
                "guild_id": interaction.guild_id,
                "vanity": {},
                "notification_channel_id": None
            }

        # Update vanity settings
        settings["vanity"].update({
            "url": status,
            "role_name": role.name
        })

        # Save settings
        await save_guild_settings(interaction.guild_id, settings)

        # Send confirmation message
        await interaction.followup.send(
            f"✅ Vanity configuration updated:\n• Status: `{status}`\n• Role: {role.mention}\n\nUse /edit_vanity_notification to customize your notification messages!",
            ephemeral=True
        )

    except discord.Forbidden:
        await interaction.followup.send(
            "❌ I don't have the required permissions to perform this action.",
            ephemeral=True
        )
    except Exception as e:
        logging.error(f"Error in set_vanity: {str(e)}")
        await interaction.followup.send(
            "❌ An error occurred while setting up the vanity configuration. Please check the logs.",
            ephemeral=True
        )

@tree.command(name="edit_vanity_notification", description="Edit vanity notification messages")
@app_commands.default_permissions(administrator=True)
async def edit_vanity_notification(interaction: discord.Interaction):
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        return

    # Get current guild settings
    settings = await get_guild_settings(interaction.guild_id)
    if not settings:
        settings = {
            "guild_id": interaction.guild_id,
            "vanity": {},
            "notification_channel_id": None
        }

    # Show the notification customization modal
    modal = VanityNotificationModal()
    await interaction.response.send_modal(modal)

@tree.command(name="set_notification_channel", description="Set the notification channel")
@app_commands.default_permissions(administrator=True)
async def set_notification_channel(interaction: discord.Interaction, channel: discord.TextChannel):
    if not log_permission_check(interaction, "set_notification_channel"):
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        return
    try:
        # Defer the response to prevent timeout
        await interaction.response.defer(ephemeral=True)

        # Get current guild settings
        settings = await get_guild_settings(interaction.guild_id)
        if not settings:
            settings = {
                "guild_id": interaction.guild_id,
                "vanity": {},
                "notification_channel_id": None
            }

        # Update notification channel
        settings["notification_channel_id"] = channel.id

        # Save to guild settings collection
        success = await save_guild_settings(interaction.guild_id, settings)

        if not success:
            await interaction.followup.send(
                "❌ Failed to save notification channel settings. Please try again.",
                ephemeral=True
            )
            return

        await interaction.followup.send(
            f"✅ Notification channel has been set to {channel.mention}",
            ephemeral=True
        )
    except Exception as e:
        logging.error(f"Error in set_notification_channel: {str(e)}")
        await interaction.followup.send(
            "❌ An error occurred while setting the notification channel. Please check the logs.",
            ephemeral=True
        )

class VanityNotificationModal(Modal):
    def __init__(self):
        super().__init__(title="Customize Vanity Notifications")

        self.add_item(TextInput(
            label="Add Role Title",
            placeholder="Enter title for when role is added",
            default="Priority Queue Granted",
            required=True
        ))

        self.add_item(TextInput(
            label="Add Role Message",
            placeholder="Use {member} for user mention and {status} for vanity status",
            default="{member} just added '{status}' as their custom Discord status and received free priority queue!",
            style=discord.TextStyle.paragraph,
            required=True
        ))

        self.add_item(TextInput(
            label="Remove Role Title",
            placeholder="Enter title for when role is removed",
            default="Priority Queue Removed",
            required=True
        ))

        self.add_item(TextInput(
            label="Remove Role Message",
            placeholder="Use {member} for user mention and {status} for vanity status",
            default="{member} has removed '{status}' from their custom Discord status",
            style=discord.TextStyle.paragraph,
            required=True
        ))

    async def on_submit(self, interaction: discord.Interaction):
        try:
            settings = await get_guild_settings(interaction.guild_id)
            if not settings:
                settings = {
                    "guild_id": interaction.guild_id,
                    "vanity": {},
                    "notification_channel_id": None
                }

            settings["vanity"].update({
                "add_role_title": self.children[0].value,
                "add_role_message": self.children[1].value,
                "remove_role_title": self.children[2].value,
                "remove_role_message": self.children[3].value
            })

            # Remove old fields if they exist
            if "embed_title" in settings["vanity"]:
                del settings["vanity"]["embed_title"]
            if "notification_message" in settings["vanity"]:
                del settings["vanity"]["notification_message"]

            await save_guild_settings(interaction.guild_id, settings)
            await interaction.response.send_message("✅ Vanity notification messages have been updated!", ephemeral=True)
        except Exception as e:
            logging.error(f"Error in VanityNotificationModal.on_submit: {str(e)}")
            await interaction.response.send_message("❌ An error occurred while saving notification settings.", ephemeral=True)

@tree.command(name="remove_vanity", description="Remove the vanity status and role")
@app_commands.default_permissions(administrator=True)
async def remove_vanity(interaction: discord.Interaction):
    if not log_permission_check(interaction, "remove_vanity"):
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        return
    try:
        # Get current guild settings
        settings = await get_guild_settings(interaction.guild_id)

        # Reset vanity settings while preserving notification messages
        if "vanity" in settings:
            notification_settings = {
                key: settings["vanity"][key]
                for key in ["add_role_title", "add_role_message", "remove_role_title", "remove_role_message"]
                if key in settings["vanity"]
            }
            settings["vanity"] = {
                "url": None,
                "role_name": None,
                **notification_settings
            }

        # Save updated settings
        await save_guild_settings(interaction.guild_id, settings)

        # Send confirmation message
        await interaction.response.send_message("✅ Vanity status has been removed successfully.", ephemeral=True)
    except Exception as e:
        logging.error(f"Error in remove_vanity: {str(e)}")
        await interaction.response.send_message("❌ An error occurred while removing the vanity configuration.", ephemeral=True)

# In the embed message section
class EmbedModal(Modal):
    def __init__(self):
        super().__init__(title="Create Embed Message")

        self.add_item(TextInput(
            label="Title",
            placeholder="Enter the title for your embed",
            required=True
        ))

        self.add_item(TextInput(
            label="Description",
            placeholder="Enter the description for your embed",
            style=discord.TextStyle.paragraph,
            required=True
        ))

        self.add_item(TextInput(
            label="Color (Hex)",
            placeholder="#RRGGBB (e.g., #FF0000 for red) - Leave empty for default",
            required=False
        ))

        self.add_item(TextInput(
            label="Image URL",
            placeholder="Enter an image URL (optional)",
            required=False
        ))

        self.add_item(TextInput(
            label="Footer",
            placeholder="Enter footer text (optional)",
            required=False
        ))

    async def on_submit(self, interaction: discord.Interaction):
        self.interaction = interaction



@tree.command(name="send_embed", description="Send an embedded message to a channel")
@app_commands.default_permissions(administrator=True)
async def send_embed(interaction: discord.Interaction, channel: discord.TextChannel, mention: str = None):
    if not log_permission_check(interaction, "send_embed"):
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        return
    try:
        # Send the modal
        modal = EmbedModal()
        await interaction.response.send_modal(modal)

        # Wait for modal submission
        await modal.wait()

        # Get values from modal
        title = modal.children[0].value
        description = modal.children[1].value
        color_hex = modal.children[2].value
        image_url = modal.children[3].value
        footer = modal.children[4].value

        # Create embed
        try:
            embed_color = int(color_hex.strip('#'), 16) if color_hex and color_hex.startswith('#') else discord.Color.blue().value
        except ValueError:
            embed_color = discord.Color.blue().value

        embed = discord.Embed(
            title=title,
            description=description,
            color=embed_color
        )

        # Set image if provided and valid
        if image_url and image_url.strip():
            image_url = image_url.strip()
            if image_url.startswith('http://') or image_url.startswith('https://'):
                try:
                    # Validate that the URL is accessible and is an image
                    async with aiohttp.ClientSession() as session:
                        async with session.head(image_url, timeout=aiohttp.ClientTimeout(total=10)) as response:
                            if response.status == 200:
                                content_type = response.headers.get('content-type', '').lower()
                                if content_type.startswith('image/'):
                                    embed.set_image(url=image_url)
                                else:
                                    await modal.interaction.followup.send(
                                        "⚠️ Warning: The provided URL does not appear to be an image. The embed was sent without the image.",
                                        ephemeral=True
                                    )
                            else:
                                await modal.interaction.followup.send(
                                    f"⚠️ Warning: Could not access the image URL (HTTP {response.status}). The embed was sent without the image.",
                                    ephemeral=True
                                )
                except Exception as e:
                    logging.warning(f"Error validating image URL {image_url}: {e}")
                    await modal.interaction.followup.send(
                        "⚠️ Warning: Could not validate the image URL. The embed was sent without the image.",
                        ephemeral=True
                    )
            else:
                await modal.interaction.followup.send(
                    "⚠️ Warning: Image URL must start with http:// or https://. The embed was sent without the image.",
                    ephemeral=True
                )

        # Set footer if provided
        if footer:
            embed.set_footer(text=footer)

        # Handle mentions
        content = None
        if mention:
            mention = mention.strip().lower()
            if mention == "@everyone" or mention == "everyone":
                content = "@everyone"  # Handle both with and without @ prefix
            elif mention == "@here" or mention == "here":
                content = "@here"  # Handle both with and without @ prefix
            elif mention.startswith("<@") and mention.endswith(">"):
                content = mention
            else:
                # Try to find role by name
                role = discord.utils.get(interaction.guild.roles, name=mention)
                if role:
                    content = role.mention
                else:
                    # Try to find user by name
                    user = discord.utils.get(interaction.guild.members, name=mention)
                    if user:
                        content = user.mention
                    else:
                        await interaction.followup.send(f"Could not find user or role with name: {mention}", ephemeral=True)
                        return

        # Send the embed with allowed mentions
        await channel.send(
            content=content,
            embed=embed,
            allowed_mentions=discord.AllowedMentions(everyone=True, roles=True, users=True)
        )

        # Send confirmation
        await modal.interaction.response.send_message(f"Embed sent to {channel.mention} successfully!", ephemeral=True)

    except discord.Forbidden:
        await modal.interaction.response.send_message("I don't have permission to send messages in that channel.", ephemeral=True)
    except Exception as e:
        logging.error(f"Error in send_embed: {e}")
        await modal.interaction.response.send_message(f"An error occurred: {str(e)}", ephemeral=True)




async def delete_command_messages(interaction):
    """Delete messages related to the command, excluding the final confirmation message."""
    try:
        messages_to_delete = []
        async for message in interaction.channel.history(limit=100):
            # Check if the message is from the user who initiated the command or the bot
            if message.author == interaction.user or message.author == bot.user:
                # Check if the message is part of the command conversation
                if message.id == interaction.id or message.content.startswith("Let's create your embed!"):
                    messages_to_delete.append(message)

        # Delete messages with delay to avoid rate limits
        for message in messages_to_delete:
            try:
                await message.delete()
                await asyncio.sleep(0.5)  # Add 500ms delay between deletions
            except discord.NotFound:
                continue  # Message already deleted
            except discord.Forbidden:
                print(f"Missing permissions to delete message {message.id}")
                continue
            except Exception as e:
                print(f"Error deleting message {message.id}: {e}")
                continue

    except Exception as e:
        print(f"Error in delete_command_messages: {e}")

async def ask_for_input(interaction, prompt):
    """Helper function to ask for user input."""
    await interaction.channel.send(prompt)
    try:
        response = await bot.wait_for('message', timeout=60.0, check=lambda m: m.author == interaction.user)
        return response.content
    except asyncio.TimeoutError:
        await interaction.channel.send("You took too long to respond!")
        return None

async def ask_for_channel(interaction, prompt):
    """Helper function to ask for a channel mention."""
    await interaction.channel.send(prompt)
    try:
        response = await bot.wait_for('message', timeout=60.0, check=lambda m: m.author == interaction.user)
        channel_id = int(response.content.strip('<>#'))
        return interaction.guild.get_channel(channel_id)
    except (ValueError, TypeError):
        await interaction.channel.send("Invalid channel mention. Please try again.")
        return None
    except asyncio.TimeoutError:
        await interaction.channel.send("You took too long to respond!")
        return None

# Auto Role System
@tree.command(name="set_join_role", description="Set the join role")
@app_commands.default_permissions(administrator=True)
async def set_join_role(interaction: discord.Interaction, role: discord.Role):
    if not log_permission_check(interaction, "set_join_role"):
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        return
    global join_role_id
    join_role_id = role.id
    await save_data()  # Save data after setting the welcome channel
    await interaction.response.send_message(f"Join role set to '{role.name}'.")

@bot.event
async def on_member_join(member):
    if join_role_id:
        role = member.guild.get_role(join_role_id)
        if role:
            await member.add_roles(role)
            print(f"Assigned join role '{role.name}' to {member.name}.")

# Gang Management System
@tree.command(name="create_gang", description="Create a new gang")
@app_commands.default_permissions(administrator=True)
@app_commands.describe(
    gang_name="Name of the gang to create",
    leader="Member who will lead the gang",
    leader_role="Discord role for the gang leader",
    member_limit="Maximum number of members allowed"
)
async def create_gang(interaction: discord.Interaction, gang_name: str, leader: discord.Member, leader_role: discord.Role, member_limit: int):
    log_command_execution(interaction, f"create_gang ({gang_name})")

    if gang_name in gang_roles:
        # Create professional error embed for existing gang
        error_embed = discord.Embed(
            title="❌ Gang Creation Failed",
            description=f"Gang **{gang_name}** already exists in the system.",
            color=0x000000  # Professional black theme
        )

        error_embed.add_field(
            name="**Error Details**",
            value=f"```Gang Name: {gang_name}\nStatus: Already Exists\nAction: No changes made```",
            inline=False
        )

        error_embed.add_field(
            name="**💡 Suggestion**",
            value="Choose a different gang name or use `/edit_gang` to modify the existing gang.",
            inline=False
        )

        current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
        error_embed.set_footer(text=f"Gang Management System • {current_time}")

        await interaction.response.send_message(embed=error_embed)
        return

    try:
        # Log gang creation to file for debugging
        logging.info(f"Creating gang '{gang_name}' with leader {leader.name} (ID: {leader.id})")

        # Assign the leader role
        await leader.add_roles(leader_role)
        logging.info(f"Assigned role '{leader_role.name}' to {leader.name}")

        # Store gang data with new permission system
        gang_roles[gang_name] = {
            "leader": leader.id,  # Store as integer
            "leader_role": leader_role.id,
            "members": [],
            "member_limit": member_limit,
            "current_members": 0,
            "member_management_permissions": "leader_only",  # Default to leader only
            "officers": []  # List of officer member IDs for leader_officers permission
        }

        # Store leader data
        gang_leaders[leader.id] = leader_role.id  # Store as integer, not string

        # Log successful creation
        logging.info(f"Gang '{gang_name}' created successfully with {member_limit} member limit")

        await save_data()

        # Create professional gang creation success embed with permission information
        creation_embed = discord.Embed(
            title="🏴‍☠️ Gang Created Successfully",
            description=f"Gang **{gang_name}** has been established and is now operational.",
            color=0x000000  # Professional black theme
        )

        # Add elegant separator for visual hierarchy
        creation_embed.add_field(
            name="\u200b",  # Invisible field name for spacing
            value="━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━",
            inline=False
        )

        creation_embed.add_field(
            name="**Gang Details**",
            value=f"```Gang Name: {gang_name}\nMember Limit: {member_limit}\nCurrent Members: 0/{member_limit}```",
            inline=False
        )

        creation_embed.add_field(
            name="**Leadership Information**",
            value=f"**Leader:** {leader.mention}\n**Leader Role:** {leader_role.mention}",
            inline=False
        )

        creation_embed.add_field(
            name="**🔐 Member Management**",
            value="```Only the gang leader can add/remove members```",
            inline=False
        )

        creation_embed.add_field(
            name="**🎉 Congratulations**",
            value="Your gang has been successfully created! You can now start recruiting members and building your organization.",
            inline=False
        )

        # Add elegant separator at the bottom
        creation_embed.add_field(
            name="\u200b",  # Invisible field name for spacing
            value="━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━",
            inline=False
        )

        current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
        creation_embed.set_footer(text=f"Gang Management System • {current_time}")

        await interaction.response.send_message(embed=creation_embed)

    except discord.Forbidden:
        # Create professional error embed for permission issues
        error_embed = discord.Embed(
            title="❌ Permission Error",
            description="I don't have permission to assign roles for gang creation.",
            color=0x000000  # Professional black theme
        )

        error_embed.add_field(
            name="**Permission Issue**",
            value="```Required Permission: Manage Roles\nBot Status: Permission Denied\nAction: Gang creation failed```",
            inline=False
        )

        error_embed.add_field(
            name="**💡 Solution**",
            value="Please ensure the bot has 'Manage Roles' permission and that the bot's role is higher than the roles being assigned.",
            inline=False
        )

        current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
        error_embed.set_footer(text=f"Gang Management System • {current_time}")

        await interaction.response.send_message(embed=error_embed)
    except Exception as e:
        # Create professional error embed for general errors
        error_embed = discord.Embed(
            title="❌ Gang Creation Error",
            description="An unexpected error occurred while creating the gang.",
            color=0x000000  # Professional black theme
        )

        error_embed.add_field(
            name="**Error Information**",
            value=f"```Error Type: {type(e).__name__}\nError Details: {str(e)}\nAction: Gang creation failed```",
            inline=False
        )

        error_embed.add_field(
            name="**💡 Support**",
            value="Please contact an administrator if this error persists.",
            inline=False
        )

        current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
        error_embed.set_footer(text=f"Gang Management System • {current_time}")

        await interaction.response.send_message(embed=error_embed)

@tree.command(name="assign_member_role", description="Assign a member role to a gang leader role")
@app_commands.default_permissions(administrator=True)
async def assign_member_role(interaction: discord.Interaction, leader_role: discord.Role, member_role: discord.Role):
    # Check if the leader role is valid
    if leader_role.id not in gang_leaders.values():
        # Create professional error embed for invalid leader role
        error_embed = discord.Embed(
            title="❌ Invalid Leader Role",
            description=f"The role **{leader_role.name}** is not configured as a gang leader role.",
            color=0x000000  # Professional black theme
        )

        error_embed.add_field(
            name="**Role Status**",
            value=f"```Role: {leader_role.name}\nType: Not a Leader Role\nAction: Role assignment failed```",
            inline=False
        )

        error_embed.add_field(
            name="**💡 Solution**",
            value="Only roles that are configured as gang leader roles can have member roles assigned to them.",
            inline=False
        )

        current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
        error_embed.set_footer(text=f"Gang Management System • {current_time}")

        await interaction.response.send_message(embed=error_embed)
        return

    # Ensure the gang_roles dictionary has an entry for the leader role
    gang_name = next((name for name, details in gang_roles.items() if details["leader_role"] == leader_role.id), None)
    if gang_name is None:
        # Create professional error embed for no gang found
        error_embed = discord.Embed(
            title="❌ Gang Not Found",
            description=f"No gang found for the leader role **{leader_role.name}**.",
            color=0x000000  # Professional black theme
        )

        error_embed.add_field(
            name="**Role Information**",
            value=f"```Leader Role: {leader_role.name}\nStatus: Not Associated with Gang\nAction: Role assignment failed```",
            inline=False
        )

        error_embed.add_field(
            name="**💡 Solution**",
            value="Ensure the leader role is properly configured for a gang using `/create_gang`.",
            inline=False
        )

        current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
        error_embed.set_footer(text=f"Gang Management System • {current_time}")

        await interaction.response.send_message(embed=error_embed)
        return

    # Check if the member role is already associated
    if member_role.id in gang_roles[gang_name]["members"]:
        # Create professional error embed for already associated role
        error_embed = discord.Embed(
            title="❌ Role Already Associated",
            description=f"The role **{member_role.name}** is already associated with gang **{gang_name}**.",
            color=0x000000  # Professional black theme
        )

        error_embed.add_field(
            name="**Association Status**",
            value=f"```Gang: {gang_name}\nLeader Role: {leader_role.name}\nMember Role: {member_role.name}\nStatus: Already Associated```",
            inline=False
        )

        error_embed.add_field(
            name="**ℹ️ Information**",
            value="This member role is already configured for the specified gang.",
            inline=False
        )

        current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
        error_embed.set_footer(text=f"Gang Management System • {current_time}")

        await interaction.response.send_message(embed=error_embed)
        return

    # Add the member role to the gang's associated roles
    gang_roles[gang_name]["members"].append(member_role.id)
    await save_data()  # Save the updated gang roles

    # Create professional role assignment success embed
    assignment_embed = discord.Embed(
        title="🎭 Role Assignment Successful",
        description=f"Member role successfully associated with gang leadership.",
        color=0x000000  # Professional black theme
    )

    assignment_embed.add_field(
        name="**Assignment Details**",
        value=f"```Gang: {gang_name}\nLeader Role: {leader_role.name}\nMember Role: {member_role.name}```",
        inline=False
    )

    assignment_embed.add_field(
        name="**✅ Configuration Complete**",
        value="Gang members can now be assigned this role when added to the gang.",
        inline=False
    )

    current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
    assignment_embed.set_footer(text=f"Gang Management System • {current_time}")

    await interaction.response.send_message(embed=assignment_embed)

@tree.command(name="manage_gang", description="Manage gang members")
async def manage_gang(interaction: discord.Interaction, member: discord.Member, action: str):
    """
    Manage gang members by adding or removing them.

    Parameters:
    -----------
    member: The member to add/remove
    action: Either 'add' or 'remove'
    """
    try:
        await interaction.response.defer()

        # Find gang where user is the leader
        gang_name = None
        user_id = interaction.user.id

        # Check all gangs to find one where the user is the leader
        for name, details in gang_roles.items():
            if details["leader"] == user_id:
                gang_name = name
                break

        if gang_name is None:
            # Create professional permission error embed
            permission_embed = discord.Embed(
                title="❌ Insufficient Permissions",
                description="You do not have permission to add or remove members from any gang.",
                color=0x000000  # Professional black theme
            )

            permission_embed.add_field(
                name="**Permission Requirements**",
                value="```To manage gang members, you must be:\n• A gang leader```",
                inline=False
            )

            permission_embed.add_field(
                name="**💡 Contact Information**",
                value="Only gang leaders can add or remove members. Contact server administrators if you need assistance.",
                inline=False
            )

            current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
            permission_embed.set_footer(text=f"Gang Management System • {current_time}")

            await interaction.followup.send(embed=permission_embed, ephemeral=True)
            return

        if action == "add":
            # Check member limit before adding
            if gang_roles[gang_name]["current_members"] >= gang_roles[gang_name]["member_limit"]:
                # Create professional error embed for member limit reached
                error_embed = discord.Embed(
                    title="❌ Member Limit Reached",
                    description=f"Cannot add more members to gang **{gang_name}**.",
                    color=0x000000  # Professional black theme
                )

                error_embed.add_field(
                    name="**Capacity Information**",
                    value=f"```Current Members: {gang_roles[gang_name]['current_members']}\nMember Limit: {gang_roles[gang_name]['member_limit']}\nAvailable Slots: 0```",
                    inline=False
                )

                error_embed.add_field(
                    name="**💡 Solution**",
                    value="Contact an administrator to increase the member limit or remove inactive members.",
                    inline=False
                )

                current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
                error_embed.set_footer(text=f"Gang Management System • {current_time}")

                await interaction.followup.send(embed=error_embed)
                return

            if member.id not in gang_roles[gang_name]["members"]:
                # Get all member roles associated with the gang
                member_role_ids = [role_id for role_id in gang_roles[gang_name]["members"] if isinstance(role_id, int)]

                if not member_role_ids:
                    await interaction.followup.send("No member roles are associated with this gang. Please add a member role first.")
                    return

                # Try to find a valid member role
                member_role = None
                for role_id in member_role_ids:
                    role = interaction.guild.get_role(role_id)
                    if role:
                        member_role = role
                        break

                if member_role:
                    # Send gang invitation instead of directly adding
                    success, message = await send_gang_invitation(gang_name, interaction.user, member, interaction)

                    if success:
                        # Create professional response embed for invitation sent
                        response_embed = discord.Embed(
                            title="📨 Gang Invitation Sent",
                            description=f"Successfully sent gang invitation to **{member.display_name}**.",
                            color=0x000000  # Professional black theme
                        )

                        response_embed.add_field(
                            name="**Invitation Details**",
                            value=f"```Target Member: {member.display_name}\nGang: {gang_name}\nStatus: Invitation Pending\nCurrent Count: {gang_roles[gang_name]['current_members']}/{gang_roles[gang_name]['member_limit']}```",
                            inline=False
                        )

                        response_embed.add_field(
                            name="**📋 Next Steps**",
                            value=f"**{member.display_name}** will receive a DM with Accept/Decline buttons. They can respond at any time to join or decline the invitation.",
                            inline=False
                        )

                        response_embed.add_field(
                            name="**✅ Invitation Status**",
                            value="The invitation has been sent and will remain active until the user responds.",
                            inline=False
                        )

                        current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
                        response_embed.set_footer(text=f"Gang Management System • {current_time}")

                        await interaction.followup.send(embed=response_embed)
                    else:
                        # Create professional error embed for invitation failure
                        error_embed = discord.Embed(
                            title="❌ Invitation Failed",
                            description=f"Could not send gang invitation to **{member.display_name}**.",
                            color=0x000000  # Professional black theme
                        )

                        error_embed.add_field(
                            name="**Error Details**",
                            value=f"```Target Member: {member.display_name}\nGang: {gang_name}\nError: {message}```",
                            inline=False
                        )

                        error_embed.add_field(
                            name="**💡 Common Solutions**",
                            value="• Ask the user to enable DMs from server members\n• Try again after they adjust their privacy settings\n• Contact the user through other means to inform them",
                            inline=False
                        )

                        current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
                        error_embed.set_footer(text=f"Gang Management System • {current_time}")

                        await interaction.followup.send(embed=error_embed)
                else:
                    # Create professional error embed for no valid member role
                    error_embed = discord.Embed(
                        title="❌ Configuration Error",
                        description="Could not find a valid member role for this gang.",
                        color=0x000000  # Professional black theme
                    )

                    error_embed.add_field(
                        name="**Configuration Issue**",
                        value=f"```Gang: {gang_name}\nIssue: No member role configured\nAction: Member addition failed```",
                        inline=False
                    )

                    error_embed.add_field(
                        name="**💡 Solution**",
                        value="Contact an administrator to configure member roles for this gang using `/assign_member_role`.",
                        inline=False
                    )

                    current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
                    error_embed.set_footer(text=f"Gang Management System • {current_time}")

                    await interaction.followup.send(embed=error_embed)
            else:
                # Create professional error embed for already a member
                error_embed = discord.Embed(
                    title="❌ Already a Member",
                    description=f"**{member.display_name}** is already a member of gang **{gang_name}**.",
                    color=0x000000  # Professional black theme
                )

                error_embed.add_field(
                    name="**Member Status**",
                    value=f"```Member: {member.display_name}\nGang: {gang_name}\nStatus: Already Active Member```",
                    inline=False
                )

                error_embed.add_field(
                    name="**ℹ️ Information**",
                    value="This user is already an active member of the specified gang.",
                    inline=False
                )

                current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
                error_embed.set_footer(text=f"Gang Management System • {current_time}")

                await interaction.followup.send(embed=error_embed)

        elif action == "remove":
            if member.id in gang_roles[gang_name]["members"]:
                # Get member role IDs
                member_role_ids = [role_id for role_id in gang_roles[gang_name]["members"] if isinstance(role_id, int)]

                # Remove member roles
                for role_id in member_role_ids:
                    role = interaction.guild.get_role(role_id)
                    if role and role in member.roles:
                        await member.remove_roles(role)

                gang_roles[gang_name]["members"].remove(member.id)
                gang_roles[gang_name]["current_members"] -= 1
                await save_data()

                # Send targeted notifications for member removal
                await send_targeted_member_notification(gang_name, "member_removed", interaction, target_member=member)

                # Create professional response embed for the command interface
                response_embed = discord.Embed(
                    title="👥 Member Removed Successfully",
                    description=f"Successfully removed **{member.display_name}** from gang **{gang_name}**.",
                    color=0x000000  # Professional black theme
                )

                response_embed.add_field(
                    name="**Removal Details**",
                    value=f"```Former Member: {member.display_name}\nCurrent Count: {gang_roles[gang_name]['current_members']}/{gang_roles[gang_name]['member_limit']}```",
                    inline=False
                )

                response_embed.add_field(
                    name="**✅ Notification Status**",
                    value="Targeted notifications have been sent to relevant parties (you, the removed member, and gang leader if applicable).",
                    inline=False
                )

                current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
                response_embed.set_footer(text=f"Gang Management System • {current_time}")

                await interaction.followup.send(embed=response_embed)
            else:
                # Create professional error embed for not a member
                error_embed = discord.Embed(
                    title="❌ Not a Member",
                    description=f"**{member.display_name}** is not a member of gang **{gang_name}**.",
                    color=0x000000  # Professional black theme
                )

                error_embed.add_field(
                    name="**Member Status**",
                    value=f"```Member: {member.display_name}\nGang: {gang_name}\nStatus: Not a Member```",
                    inline=False
                )

                error_embed.add_field(
                    name="**ℹ️ Information**",
                    value="This user is not currently a member of the specified gang.",
                    inline=False
                )

                current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
                error_embed.set_footer(text=f"Gang Management System • {current_time}")

                await interaction.followup.send(embed=error_embed)

        else:
            # Create professional error embed for invalid action
            error_embed = discord.Embed(
                title="❌ Invalid Action",
                description="The specified action is not valid for member management.",
                color=0x000000  # Professional black theme
            )

            error_embed.add_field(
                name="**Valid Actions**",
                value="```add    - Add a member to the gang\nremove - Remove a member from the gang```",
                inline=False
            )

            error_embed.add_field(
                name="**💡 Usage**",
                value="Please specify either 'add' or 'remove' as the action parameter.",
                inline=False
            )

            current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
            error_embed.set_footer(text=f"Gang Management System • {current_time}")

            await interaction.followup.send(embed=error_embed)

    except discord.Forbidden:
        logging.error(f"Permission error in manage_gang: {interaction.guild.id}")
        await interaction.followup.send("I don't have permission to manage roles.", ephemeral=True)
    except discord.HTTPException as e:
        logging.error(f"Discord API error in manage_gang: {e}")
        await interaction.followup.send(f"Discord API error: {str(e)}", ephemeral=True)
    except Exception as e:
        logging.error(f"Error in manage_gang: {e}")
        await interaction.followup.send("An error occurred while managing the gang.", ephemeral=True)

@tree.command(name="remove_gang", description="Remove a gang")
@app_commands.default_permissions(administrator=True)
async def remove_gang(interaction: discord.Interaction, name: str):
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        return
    if name in gang_roles:
        del gang_roles[name]
        # Also remove from gang_leaders if they exist
        gang_leader_id = gang_leaders.pop(next((id for id, role in gang_leaders.items() if role == name), None), None)
        if gang_leader_id:
            del gang_leaders[gang_leader_id]
        await save_data()  # Save the updated gang roles

        # Create professional gang removal success embed
        removal_embed = discord.Embed(
            title="🗑️ Gang Removed Successfully",
            description=f"Gang **{name}** has been permanently dissolved.",
            color=0x000000  # Professional black theme
        )

        removal_embed.add_field(
            name="**Removal Details**",
            value=f"```Gang Name: {name}\nStatus: Permanently Dissolved\nData: Completely Removed```",
            inline=False
        )

        removal_embed.add_field(
            name="**⚠️ Important Notice**",
            value="All gang data, roles, and member associations have been permanently removed from the system.",
            inline=False
        )

        current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
        removal_embed.set_footer(text=f"Gang Management System • {current_time}")

        await interaction.response.send_message(embed=removal_embed)
    else:
        # Create professional error embed for gang not found
        error_embed = discord.Embed(
            title="❌ Gang Not Found",
            description=f"Gang **{name}** does not exist in the system.",
            color=0x000000  # Professional black theme
        )

        error_embed.add_field(
            name="**Error Details**",
            value=f"```Requested Gang: {name}\nStatus: Not Found\nAction: No changes made```",
            inline=False
        )

        error_embed.add_field(
            name="**💡 Suggestion**",
            value="Use `/check_gang_list` to view all existing gangs in the system.",
            inline=False
        )

        current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
        error_embed.set_footer(text=f"Gang Management System • {current_time}")

        await interaction.response.send_message(embed=error_embed)

@tree.command(name="check_gang_list", description="Check the list of gangs and their members")
@app_commands.default_permissions(administrator=True)
async def check_gang_list(interaction: discord.Interaction):
    if not gang_roles:
        # Create professional "no gangs" embed
        no_gangs_embed = discord.Embed(
            title="📋 Gang List",
            description="No gangs are currently registered in the system.",
            color=0x000000  # Professional black theme
        )

        no_gangs_embed.add_field(
            name="**System Status**",
            value="```No Active Gangs\nTotal Gangs: 0\nStatus: Empty```",
            inline=False
        )

        no_gangs_embed.add_field(
            name="**💡 Getting Started**",
            value="Use `/create_gang` to establish the first gang in your server.",
            inline=False
        )

        current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
        no_gangs_embed.set_footer(text=f"Gang Management System • {current_time}")

        await interaction.response.send_message(embed=no_gangs_embed)
        return

    # Create professional gang list embed
    embed = discord.Embed(
        title="🏴‍☠️ Gang Registry",
        description=f"Complete overview of all {len(gang_roles)} registered gangs in the system.",
        color=0x000000  # Professional black theme
    )

    # Add elegant separator for visual hierarchy
    embed.add_field(
        name="\u200b",  # Invisible field name for spacing
        value="━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━",
        inline=False
    )

    for gang_name, details in gang_roles.items():
        leader = interaction.guild.get_member(details["leader"])
        members = [interaction.guild.get_member(member_id) for member_id in details["members"] if isinstance(member_id, int)]
        members_list = ", ".join(member.mention for member in members if member) or "No members"

        strike_count = gang_strikes.get(gang_name, 0)
        strike_text = f"⚠️ {strike_count} Strikes" if strike_count > 0 else "✅ Clean Record"

        # Get permission description
        permission_level = details.get("member_management_permissions", "leader_only")
        permission_descriptions = {
            "leader_only": "Leader Only",
            "leader_officers": "Leader + Officers",
            "all_members": "All Members"
        }
        permission_text = permission_descriptions.get(permission_level, "Unknown")

        embed.add_field(
            name=f"🏴‍☠️ **{gang_name}**",
            value=(
                f"**Leader:** {leader.mention if leader else 'Unknown'}\n"
                f"**Members:** {details['current_members']}/{details['member_limit']}\n"
                f"**Status:** {strike_text}\n"
                f"**Member Management:** {permission_text}\n"
                f"**Member List:** {members_list}"
            ),
            inline=False
        )

        # Add separator between gangs
        embed.add_field(
            name="\u200b",  # Invisible field name for spacing
            value="━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━",
            inline=False
        )

    # Add summary information
    total_members = sum(details['current_members'] for details in gang_roles.values())
    total_strikes = sum(gang_strikes.values())

    embed.add_field(
        name="**📊 System Summary**",
        value=f"```Total Gangs: {len(gang_roles)}\nTotal Members: {total_members}\nTotal Strikes: {total_strikes}```",
        inline=False
    )

    current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
    embed.set_footer(text=f"Gang Management System • {current_time}")

    await interaction.response.send_message(embed=embed)

# Add this command in the Gang Management System section
@tree.command(name="remove_strike", description="Remove a strike from a gang")
@app_commands.default_permissions(administrator=True)
async def remove_strike(interaction: discord.Interaction, gang_name: str, reason: str = None):
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        return
    if gang_name not in gang_roles:
        # Create professional error embed for gang not found
        error_embed = discord.Embed(
            title="❌ Gang Not Found",
            description=f"Gang **{gang_name}** does not exist in the system.",
            color=0x000000  # Professional black theme
        )

        error_embed.add_field(
            name="**Error Details**",
            value=f"```Requested Gang: {gang_name}\nStatus: Not Found\nAction: Strike removal failed```",
            inline=False
        )

        error_embed.add_field(
            name="**💡 Suggestion**",
            value="Use `/check_gang_list` to view all existing gangs in the system.",
            inline=False
        )

        current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
        error_embed.set_footer(text=f"Gang Management System • {current_time}")

        await interaction.response.send_message(embed=error_embed)
        return

    if gang_name not in gang_strikes or gang_strikes[gang_name] <= 0:
        # Create professional error embed for no strikes
        error_embed = discord.Embed(
            title="❌ No Strikes to Remove",
            description=f"Gang **{gang_name}** has no strikes that can be removed.",
            color=0x000000  # Professional black theme
        )

        error_embed.add_field(
            name="**Strike Status**",
            value=f"```Gang: {gang_name}\nCurrent Strikes: {gang_strikes.get(gang_name, 0)}\nAction: Cannot remove strikes```",
            inline=False
        )

        error_embed.add_field(
            name="**ℹ️ Information**",
            value="Gangs must have at least one strike before strikes can be removed.",
            inline=False
        )

        current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
        error_embed.set_footer(text=f"Gang Management System • {current_time}")

        await interaction.response.send_message(embed=error_embed)
        return

    gang_strikes[gang_name] -= 1
    await save_data()

    # Create professional strike removal notification embed
    embed = await create_gang_notification_embed(
        title="Strike Removed",
        description=f"Great news! A strike has been removed from your gang **{gang_name}**.",
        gang_name=gang_name,
        notification_type="strike_removed",
        current_strikes=gang_strikes[gang_name],
        reason=reason or "Administrative decision"
    )

    # Send professional embed notifications to all gang members
    await notify_gang_members_with_embed(gang_name, embed, interaction)

    # Create professional response embed for the administrator
    response_embed = discord.Embed(
        title="✅ Strike Removal Successful",
        description=f"Strike successfully removed from gang **{gang_name}**.",
        color=0x000000  # Professional black theme
    )

    response_embed.add_field(
        name="**Gang Details**",
        value=f"```Gang: {gang_name}\nCurrent Strikes: {gang_strikes[gang_name]}\nReason: {reason or 'Administrative decision'}```",
        inline=False
    )

    response_embed.add_field(
        name="**✅ Notification Status**",
        value="All gang members have been notified of this strike removal.",
        inline=False
    )

    current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
    response_embed.set_footer(text=f"Gang Management System • {current_time}")

    await interaction.response.send_message(embed=response_embed)

# Add this command for gang leaders to view their gang stats
@tree.command(name="view_my_gang", description="View your gang's statistics")
async def view_my_gang(interaction: discord.Interaction):
    try:
        await interaction.response.defer()

        # Find gang where user is leader
        user_id = interaction.user.id
        gang_name = None

        for name, details in gang_roles.items():
            if details["leader"] == user_id:
                gang_name = name
                break

        if gang_name is None:
            await interaction.followup.send("You are not a gang leader.", ephemeral=True)
            return

        gang_details = gang_roles[gang_name]
        # Get member list excluding role IDs
        member_ids = [mid for mid in gang_details["members"] if isinstance(mid, int)]
        members = [interaction.guild.get_member(member_id) for member_id in member_ids]
        members_list = ", ".join(member.mention for member in members if member) or "No members"

        # Create professional gang statistics embed
        embed = discord.Embed(
            title=f"🏴‍☠️ Gang Statistics",
            description=f"Detailed overview of your gang **{gang_name}**.",
            color=0x000000  # Professional black theme
        )

        # Add elegant separator for visual hierarchy
        embed.add_field(
            name="\u200b",  # Invisible field name for spacing
            value="━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━",
            inline=False
        )

        embed.add_field(
            name="**👥 Membership Information**",
            value=f"```Current Members: {gang_details['current_members']}/{gang_details['member_limit']}\nAvailable Slots: {gang_details['member_limit'] - gang_details['current_members']}```",
            inline=False
        )

        embed.add_field(
            name="**📋 Member Roster**",
            value=members_list,
            inline=False
        )

        # Add strike information with appropriate styling
        strike_count = gang_strikes.get(gang_name, 0)
        strike_status = "⚠️ Warning Status" if strike_count > 0 else "✅ Good Standing"

        embed.add_field(
            name="**⚖️ Disciplinary Record**",
            value=f"```Status: {strike_status}\nCurrent Strikes: {strike_count}\nRecord: {'Clean' if strike_count == 0 else 'Requires Attention'}```",
            inline=False
        )

        # Add elegant separator at the bottom
        embed.add_field(
            name="\u200b",  # Invisible field name for spacing
            value="━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━",
            inline=False
        )

        current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
        embed.set_footer(text=f"Gang Management System • {current_time}")

        await interaction.followup.send(embed=embed)

    except Exception as e:
        print(f"Error in view_my_gang: {e}")
        try:
            if not interaction.response.is_done():
                await interaction.response.send_message("An error occurred while processing your request.", ephemeral=True)
        except:
            pass

# Add these helper functions in the Gang Management System section, before the commands

async def create_gang_notification_embed(title, description, gang_name, notification_type, **kwargs):
    """Create a professional gang notification embed matching the bot's theme"""
    embed = discord.Embed(
        title=f"🏴‍☠️ {title}",
        description=description,
        color=0x000000  # Professional black theme matching ticket system
    )

    # Add elegant separator for visual hierarchy
    embed.add_field(
        name="\u200b",  # Invisible field name for spacing
        value="━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━",
        inline=False
    )

    # Add gang information
    embed.add_field(
        name="**Gang Information**",
        value=f"```{gang_name}```",
        inline=False
    )

    # Add notification-specific fields
    if notification_type == "strike_added":
        embed.add_field(
            name="**Strike Details**",
            value=f"**Current Strikes:** {kwargs.get('current_strikes', 'Unknown')}\n**Reason:** {kwargs.get('reason', 'No reason provided')}",
            inline=False
        )
        embed.add_field(
            name="**⚠️ Important Notice**",
            value="Multiple strikes may result in gang penalties or dissolution. Please review gang conduct guidelines.",
            inline=False
        )

    elif notification_type == "strike_removed":
        embed.add_field(
            name="**Strike Removal Details**",
            value=f"**Current Strikes:** {kwargs.get('current_strikes', 'Unknown')}\n**Reason:** {kwargs.get('reason', 'Administrative decision')}",
            inline=False
        )
        embed.add_field(
            name="**✅ Good News**",
            value="Your gang's conduct has been recognized. Keep up the positive behavior!",
            inline=False
        )

    elif notification_type == "member_added":
        embed.add_field(
            name="**New Member Details**",
            value=f"**Member:** {kwargs.get('member_mention', 'Unknown')}\n**Role:** {kwargs.get('role_name', 'Unknown')}\n**Current Count:** {kwargs.get('current_count', 'Unknown')}/{kwargs.get('member_limit', 'Unknown')}",
            inline=False
        )
        embed.add_field(
            name="**👥 Welcome**",
            value="Please welcome your new gang member and help them integrate into the group!",
            inline=False
        )

    elif notification_type == "member_removed":
        embed.add_field(
            name="**Member Removal Details**",
            value=f"**Former Member:** {kwargs.get('member_mention', 'Unknown')}\n**Current Count:** {kwargs.get('current_count', 'Unknown')}/{kwargs.get('member_limit', 'Unknown')}",
            inline=False
        )
        embed.add_field(
            name="**📋 Update**",
            value="Gang membership has been updated. Continue building your team!",
            inline=False
        )

    elif notification_type == "settings_changed":
        changes_text = "\n".join([f"• {change}" for change in kwargs.get('changes', [])])
        embed.add_field(
            name="**Changes Made**",
            value=f"```{changes_text}```",
            inline=False
        )
        embed.add_field(
            name="**⚙️ Administrative Update**",
            value="Gang settings have been modified by server administration. These changes are now in effect.",
            inline=False
        )

    elif notification_type == "leadership_change":
        embed.add_field(
            name="**Leadership Transition**",
            value=f"**Former Leader:** {kwargs.get('old_leader', 'Unknown')}\n**New Leader:** {kwargs.get('new_leader', 'Unknown')}",
            inline=False
        )
        embed.add_field(
            name="**👑 Leadership Change**",
            value="Gang leadership has been transferred. Please support your new leader!",
            inline=False
        )

    elif notification_type == "action_confirmation":
        embed.add_field(
            name="**Action Details**",
            value=f"**Action:** {kwargs.get('action', 'Unknown')}\n**Target:** {kwargs.get('target', 'Unknown')}\n**Current Count:** {kwargs.get('current_count', 'Unknown')}/{kwargs.get('member_limit', 'Unknown')}",
            inline=False
        )
        embed.add_field(
            name="**✅ Confirmation**",
            value="Your action has been completed successfully. All relevant parties have been notified.",
            inline=False
        )

    elif notification_type == "membership_change":
        action = kwargs.get('action', 'Unknown')
        if "Added" in action:
            embed.add_field(
                name="**Welcome Information**",
                value=f"**Added by:** {kwargs.get('added_by', 'Unknown')}\n**Current Count:** {kwargs.get('current_count', 'Unknown')}/{kwargs.get('member_limit', 'Unknown')}",
                inline=False
            )
            embed.add_field(
                name="**🎉 Welcome**",
                value="Welcome to the gang! Please familiarize yourself with gang rules and participate actively in gang activities.",
                inline=False
            )
        else:
            embed.add_field(
                name="**Removal Information**",
                value=f"**Removed by:** {kwargs.get('removed_by', 'Unknown')}\n**Former Count:** {kwargs.get('current_count', 'Unknown')}/{kwargs.get('member_limit', 'Unknown')}",
                inline=False
            )
            embed.add_field(
                name="**📋 Notice**",
                value="Your membership in this gang has ended. Thank you for your participation.",
                inline=False
            )

    elif notification_type == "leadership_notification":
        embed.add_field(
            name="**Action Summary**",
            value=f"**Action:** {kwargs.get('action', 'Unknown')}\n**Target:** {kwargs.get('target', 'Unknown')}\n**Performed by:** {kwargs.get('performed_by', 'Unknown')}\n**Current Count:** {kwargs.get('current_count', 'Unknown')}/{kwargs.get('member_limit', 'Unknown')}",
            inline=False
        )
        embed.add_field(
            name="**👑 Leadership Notice**",
            value="This action was performed by a gang member with appropriate permissions. No action required from you.",
            inline=False
        )

    # Add elegant separator at the bottom
    embed.add_field(
        name="\u200b",  # Invisible field name for spacing
        value="━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━",
        inline=False
    )

    # Add professional footer with timestamp
    current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
    embed.set_footer(text=f"Gang Management System • {current_time}")

    return embed

def check_member_management_permission(gang_name, user_id):
    """Check if a user has permission to add/remove members from a gang"""
    if gang_name not in gang_roles:
        return False, "Gang not found"

    gang_data = gang_roles[gang_name]
    permission_level = gang_data.get("member_management_permissions", "leader_only")

    # Leader always has permission
    if gang_data["leader"] == user_id:
        return True, "leader"

    # Check permission levels
    if permission_level == "leader_only":
        return False, "Only the gang leader can add/remove members"

    elif permission_level == "leader_officers":
        # Check if user is an officer
        if user_id in gang_data.get("officers", []):
            return True, "officer"
        return False, "Only the gang leader and designated officers can add/remove members"

    elif permission_level == "all_members":
        # Check if user is a member
        if user_id in gang_data["members"]:
            return True, "member"
        return False, "Only gang members can add/remove other members"

    return False, "Invalid permission configuration"

async def get_gang_notification_recipients(gang_name, guild):
    """Get all users who should be notified about gang invitation responses"""
    if gang_name not in gang_roles:
        return []

    gang_data = gang_roles[gang_name]
    recipients = []

    # Always include the gang leader
    leader_id = gang_data["leader"]
    leader = guild.get_member(leader_id)
    if leader:
        recipients.append(leader)

    # Include officers if they exist
    officers = gang_data.get("officers", [])
    for officer_id in officers:
        officer = guild.get_member(officer_id)
        if officer and officer not in recipients:
            recipients.append(officer)

    # For "all_members" permission level, include all members with manage gang permissions
    permission_level = gang_data.get("member_management_permissions", "leader_only")
    if permission_level == "all_members":
        for member_id in gang_data.get("members", []):
            member = guild.get_member(member_id)
            if member and member not in recipients:
                recipients.append(member)

    return recipients

class GangInvitationView(discord.ui.View):
    """Persistent view for gang invitation confirmations"""

    def __init__(self, invitation_id: str):
        super().__init__(timeout=None)  # Persistent view with no timeout
        self.invitation_id = invitation_id

    @discord.ui.button(label="Accept", style=discord.ButtonStyle.success, emoji="✅", custom_id="gang_invite_accept")
    async def accept_invitation(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Handle gang invitation acceptance"""
        try:
            await interaction.response.defer()

            # Get invitation details from database
            if self.invitation_id not in gang_invitations:
                # Create professional error embed for expired invitation
                error_embed = discord.Embed(
                    title="❌ Invitation Expired",
                    description="This gang invitation is no longer valid.",
                    color=0x000000  # Professional black theme
                )

                error_embed.add_field(
                    name="**Status Information**",
                    value="```Status: Invitation Expired\nReason: No longer in system\nAction: Cannot process acceptance```",
                    inline=False
                )

                error_embed.add_field(
                    name="**💡 Next Steps**",
                    value="Contact the gang leader to request a new invitation.",
                    inline=False
                )

                current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
                error_embed.set_footer(text=f"Gang Management System • {current_time}")

                await interaction.followup.send(embed=error_embed, ephemeral=True)
                return

            invitation = gang_invitations[self.invitation_id]
            gang_name = invitation["gang_name"]
            inviter_id = invitation["inviter_id"]
            target_id = invitation["target_id"]

            # Verify the user responding is the target
            if interaction.user.id != target_id:
                await interaction.followup.send("This invitation is not for you.", ephemeral=True)
                return

            # Check if gang still exists
            if gang_name not in gang_roles:
                # Create professional error embed for gang no longer exists
                error_embed = discord.Embed(
                    title="❌ Gang No Longer Exists",
                    description=f"The gang **{gang_name}** no longer exists in the system.",
                    color=0x000000  # Professional black theme
                )

                error_embed.add_field(
                    name="**Status Information**",
                    value=f"```Gang: {gang_name}\nStatus: Deleted/Disbanded\nAction: Cannot process acceptance```",
                    inline=False
                )

                error_embed.add_field(
                    name="**ℹ️ Information**",
                    value="The gang may have been disbanded or deleted after this invitation was sent.",
                    inline=False
                )

                current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
                error_embed.set_footer(text=f"Gang Management System • {current_time}")

                # Remove expired invitation
                del gang_invitations[self.invitation_id]
                await save_data()

                await interaction.followup.send(embed=error_embed, ephemeral=True)
                return

            # Check if user is already a member
            if target_id in gang_roles[gang_name]["members"]:
                # Create professional error embed for already a member
                error_embed = discord.Embed(
                    title="❌ Already a Member",
                    description=f"You are already a member of gang **{gang_name}**.",
                    color=0x000000  # Professional black theme
                )

                error_embed.add_field(
                    name="**Member Status**",
                    value=f"```Gang: {gang_name}\nStatus: Active Member\nAction: Invitation no longer needed```",
                    inline=False
                )

                current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
                error_embed.set_footer(text=f"Gang Management System • {current_time}")

                # Remove invitation since user is already a member
                del gang_invitations[self.invitation_id]
                await save_data()

                await interaction.followup.send(embed=error_embed, ephemeral=True)
                return

            # Check member limit
            if gang_roles[gang_name]["current_members"] >= gang_roles[gang_name]["member_limit"]:
                # Create professional error embed for member limit reached
                error_embed = discord.Embed(
                    title="❌ Gang Full",
                    description=f"Gang **{gang_name}** has reached its member limit.",
                    color=0x000000  # Professional black theme
                )

                error_embed.add_field(
                    name="**Capacity Information**",
                    value=f"```Current Members: {gang_roles[gang_name]['current_members']}\nMember Limit: {gang_roles[gang_name]['member_limit']}\nAvailable Slots: 0```",
                    inline=False
                )

                error_embed.add_field(
                    name="**💡 Solution**",
                    value="Contact the gang leader to increase the member limit or wait for a slot to become available.",
                    inline=False
                )

                current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
                error_embed.set_footer(text=f"Gang Management System • {current_time}")

                await interaction.followup.send(embed=error_embed, ephemeral=True)
                return

            # Add member to gang
            gang_roles[gang_name]["members"].append(target_id)
            gang_roles[gang_name]["current_members"] += 1

            # Get guild from invitation data since interaction.guild is None in DMs
            guild_id = invitation["guild_id"]
            guild = bot.get_guild(guild_id)
            if not guild:
                logging.error(f"Could not find guild {guild_id} for gang invitation")
                # Still save the member addition but skip role assignment
                await save_data()

                # Create success embed without role assignment
                success_embed = discord.Embed(
                    title="🎉 Welcome to the Gang!",
                    description=f"You have successfully joined gang **{gang_name}**!",
                    color=0x000000  # Professional black theme
                )

                success_embed.add_field(
                    name="**Gang Information**",
                    value=f"```Gang: {gang_name}\nStatus: Active Member\nCurrent Count: {gang_roles[gang_name]['current_members']}/{gang_roles[gang_name]['member_limit']}```",
                    inline=False
                )

                success_embed.add_field(
                    name="**⚠️ Note**",
                    value="You have been added to the gang, but roles could not be assigned automatically. Contact a server administrator.",
                    inline=False
                )

                current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
                success_embed.set_footer(text=f"Gang Management System • {current_time}")

                # Remove invitation from system
                del gang_invitations[self.invitation_id]
                await save_data()

                # Update the original message to show acceptance
                self.clear_items()
                await interaction.followup.edit_message(interaction.message.id, embed=success_embed, view=self)
                return

            member = guild.get_member(target_id)
            if not member:
                logging.error(f"Could not find member {target_id} in guild {guild_id}")
                # Still save the member addition but skip role assignment
                await save_data_optimized()

                # Create success embed without role assignment
                success_embed = discord.Embed(
                    title="🎉 Welcome to the Gang!",
                    description=f"You have successfully joined gang **{gang_name}**!",
                    color=0x000000  # Professional black theme
                )

                success_embed.add_field(
                    name="**Gang Information**",
                    value=f"```Gang: {gang_name}\nStatus: Active Member\nCurrent Count: {gang_roles[gang_name]['current_members']}/{gang_roles[gang_name]['member_limit']}```",
                    inline=False
                )

                success_embed.add_field(
                    name="**⚠️ Note**",
                    value="You have been added to the gang, but roles could not be assigned automatically. Contact a server administrator.",
                    inline=False
                )

                current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
                success_embed.set_footer(text=f"Gang Management System • {current_time}")

                # Remove invitation from system
                del gang_invitations[self.invitation_id]
                await save_data()

                # Update the original message to show acceptance
                self.clear_items()
                await interaction.followup.edit_message(interaction.message.id, embed=success_embed, view=self)
                return

            # Find member role for this gang
            member_role = None
            for role in guild.roles:
                if role.id in gang_roles[gang_name]["members"] and isinstance(role.id, int):
                    member_role = role
                    break

            if member_role:
                try:
                    await member.add_roles(member_role)
                    logging.info(f"Assigned role {member_role.name} to new gang member {member.display_name}")
                except Exception as e:
                    logging.warning(f"Could not assign role to new gang member: {e}")

            # Remove invitation from system
            del gang_invitations[self.invitation_id]
            await save_data()

            # Create acceptance confirmation embed
            success_embed = discord.Embed(
                title="🎉 Welcome to the Gang!",
                description=f"You have successfully joined gang **{gang_name}**!",
                color=0x000000  # Professional black theme
            )

            success_embed.add_field(
                name="**Gang Information**",
                value=f"```Gang: {gang_name}\nStatus: Active Member\nCurrent Count: {gang_roles[gang_name]['current_members']}/{gang_roles[gang_name]['member_limit']}```",
                inline=False
            )

            success_embed.add_field(
                name="**🎊 Welcome Message**",
                value="Welcome to the gang! Please familiarize yourself with gang rules and participate actively in gang activities.",
                inline=False
            )

            current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
            success_embed.set_footer(text=f"Gang Management System • {current_time}")

            # Update the original message to show acceptance
            self.clear_items()
            await interaction.followup.edit_message(interaction.message.id, embed=success_embed, view=self)

            # Notify all authorized gang members about the acceptance
            try:
                recipients = await get_gang_notification_recipients(gang_name, guild)
                inviter_member = guild.get_member(inviter_id)

                for recipient in recipients:
                    # Create personalized notification embed
                    if recipient.id == inviter_id:
                        # Special message for the person who sent the invitation
                        notification_embed = discord.Embed(
                            title="🎉 Invitation Accepted!",
                            description=f"**{member.display_name}** has accepted your invitation to join gang **{gang_name}**!",
                            color=0x00ff00  # Green color for success
                        )
                    else:
                        # General notification for other authorized members
                        notification_embed = discord.Embed(
                            title="🎉 New Member Joined",
                            description=f"**{member.display_name}** has accepted an invitation and joined gang **{gang_name}**.",
                            color=0x00ff00  # Green color for success
                        )

                    notification_embed.add_field(
                        name="**Gang Information**",
                        value=f"```Gang: {gang_name}\nNew Member: {member.display_name}\nInvited by: {inviter_member.display_name if inviter_member else 'Unknown'}\nCurrent Count: {gang_roles[gang_name]['current_members']}/{gang_roles[gang_name]['member_limit']}```",
                        inline=False
                    )

                    current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
                    notification_embed.set_footer(text=f"Gang Management System • {current_time}")

                    try:
                        await recipient.send(embed=notification_embed)
                        logging.info(f"Sent gang invitation acceptance notification to {recipient.display_name}")
                    except Exception as e:
                        logging.warning(f"Could not notify gang member {recipient.id}: {e}")

            except Exception as e:
                logging.error(f"Error sending gang invitation acceptance notifications: {e}")

        except Exception as e:
            logging.error(f"Error processing gang invitation acceptance: {e}")
            await interaction.followup.send("An error occurred while processing your acceptance. Please try again.", ephemeral=True)

    @discord.ui.button(label="Decline", style=discord.ButtonStyle.danger, emoji="❌", custom_id="gang_invite_decline")
    async def decline_invitation(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Handle gang invitation decline"""
        try:
            await interaction.response.defer()

            # Get invitation details from database
            if self.invitation_id not in gang_invitations:
                await interaction.followup.send("This invitation is no longer valid.", ephemeral=True)
                return

            invitation = gang_invitations[self.invitation_id]
            gang_name = invitation["gang_name"]
            target_id = invitation["target_id"]
            inviter_id = invitation["inviter_id"]
            guild_id = invitation["guild_id"]

            # Verify the user responding is the target
            if interaction.user.id != target_id:
                await interaction.followup.send("This invitation is not for you.", ephemeral=True)
                return

            # Get guild and member info for notifications
            guild = bot.get_guild(guild_id)
            target_member = guild.get_member(target_id) if guild else None

            # Remove invitation from system
            del gang_invitations[self.invitation_id]
            await save_data()

            # Create decline confirmation embed
            decline_embed = discord.Embed(
                title="❌ Invitation Declined",
                description=f"You have declined the invitation to join gang **{gang_name}**.",
                color=0x000000  # Professional black theme
            )

            decline_embed.add_field(
                name="**Decision Recorded**",
                value=f"```Gang: {gang_name}\nStatus: Invitation Declined\nAction: No further action required```",
                inline=False
            )

            decline_embed.add_field(
                name="**ℹ️ Information**",
                value="You can still be invited to join this gang again in the future if desired.",
                inline=False
            )

            current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
            decline_embed.set_footer(text=f"Gang Management System • {current_time}")

            # Update the original message to show decline
            self.clear_items()
            await interaction.followup.edit_message(interaction.message.id, embed=decline_embed, view=self)

            # Notify all authorized gang members about the decline
            if guild and target_member:
                try:
                    recipients = await get_gang_notification_recipients(gang_name, guild)
                    inviter_member = guild.get_member(inviter_id)

                    for recipient in recipients:
                        # Create personalized notification embed
                        if recipient.id == inviter_id:
                            # Special message for the person who sent the invitation
                            notification_embed = discord.Embed(
                                title="❌ Invitation Declined",
                                description=f"**{target_member.display_name}** has declined your invitation to join gang **{gang_name}**.",
                                color=0xff6b6b  # Red color for decline
                            )
                        else:
                            # General notification for other authorized members
                            notification_embed = discord.Embed(
                                title="❌ Invitation Declined",
                                description=f"**{target_member.display_name}** has declined an invitation to join gang **{gang_name}**.",
                                color=0xff6b6b  # Red color for decline
                            )

                        notification_embed.add_field(
                            name="**Gang Information**",
                            value=f"```Gang: {gang_name}\nDeclined by: {target_member.display_name}\nInvited by: {inviter_member.display_name if inviter_member else 'Unknown'}\nStatus: Invitation Declined```",
                            inline=False
                        )

                        notification_embed.add_field(
                            name="**ℹ️ Information**",
                            value="The user can still be invited again in the future if desired.",
                            inline=False
                        )

                        current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
                        notification_embed.set_footer(text=f"Gang Management System • {current_time}")

                        try:
                            await recipient.send(embed=notification_embed)
                            logging.info(f"Sent gang invitation decline notification to {recipient.display_name}")
                        except Exception as e:
                            logging.warning(f"Could not notify gang member {recipient.id}: {e}")

                except Exception as e:
                    logging.error(f"Error sending gang invitation decline notifications: {e}")

        except Exception as e:
            logging.error(f"Error processing gang invitation decline: {e}")
            await interaction.followup.send("An error occurred while processing your decline. Please try again.", ephemeral=True)

async def send_gang_invitation(gang_name, inviter, target_member, interaction):
    """Send a professional gang invitation with confirmation buttons"""
    try:
        # Generate unique invitation ID
        invitation_id = f"{gang_name}_{target_member.id}_{int(datetime.now().timestamp())}"

        # Store invitation in database
        gang_invitations[invitation_id] = {
            "gang_name": gang_name,
            "inviter_id": inviter.id,
            "target_id": target_member.id,
            "guild_id": interaction.guild.id,
            "created_at": datetime.now().isoformat(),
            "status": "pending"
        }
        await save_data()

        # Get gang information
        gang_data = gang_roles[gang_name]

        # Create professional invitation embed
        invitation_embed = discord.Embed(
            title="🏴‍☠️ Gang Invitation",
            description=f"You have been invited to join gang **{gang_name}**!",
            color=0x000000  # Professional black theme
        )

        invitation_embed.add_field(
            name="**Gang Information**",
            value=f"```Gang Name: {gang_name}\nCurrent Members: {gang_data['current_members']}/{gang_data['member_limit']}\nInvited by: {inviter.display_name}```",
            inline=False
        )

        invitation_embed.add_field(
            name="**📋 What This Means**",
            value="Joining this gang will grant you access to gang-specific roles, activities, and community features.",
            inline=False
        )

        invitation_embed.add_field(
            name="**⚡ Action Required**",
            value="Please click **Accept** to join the gang or **Decline** to politely refuse the invitation.",
            inline=False
        )

        current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
        invitation_embed.set_footer(text=f"Gang Management System • {current_time}")

        # Create persistent view with confirmation buttons
        view = GangInvitationView(invitation_id)

        # Send invitation to target member
        try:
            await target_member.send(embed=invitation_embed, view=view)
            return True, "Invitation sent successfully"
        except discord.Forbidden:
            # Remove invitation if we can't send DM
            del gang_invitations[invitation_id]
            await save_data()
            return False, "Could not send invitation - user has DMs disabled"
        except Exception as e:
            # Remove invitation if sending failed
            del gang_invitations[invitation_id]
            await save_data()
            return False, f"Failed to send invitation: {str(e)}"

    except Exception as e:
        logging.error(f"Error creating gang invitation: {e}")
        return False, f"Error creating invitation: {str(e)}"

async def restore_gang_invitation_views():
    """Restore persistent gang invitation views on bot startup with enhanced error handling"""
    try:
        logging.info(f"Starting gang invitation view restoration...")
        logging.info(f"Found {len(gang_invitations)} pending gang invitations")

        if not gang_invitations:
            logging.info("No pending gang invitations to restore")
            return

        restored_count = 0
        failed_count = 0

        # Register all pending gang invitation views
        for invitation_id, invitation_data in gang_invitations.items():
            try:
                # Validate invitation data
                if not isinstance(invitation_data, dict):
                    logging.warning(f"Invalid invitation data for {invitation_id}: {type(invitation_data)}")
                    failed_count += 1
                    continue

                required_fields = ["gang_name", "inviter_id", "target_id", "guild_id"]
                missing_fields = [field for field in required_fields if field not in invitation_data]

                if missing_fields:
                    logging.warning(f"Invitation {invitation_id} missing required fields: {missing_fields}")
                    failed_count += 1
                    continue

                # Create and register the view
                view = GangInvitationView(invitation_id)
                bot.add_view(view)
                restored_count += 1

                logging.info(f"Restored gang invitation view for invitation: {invitation_id} (Gang: {invitation_data.get('gang_name', 'Unknown')})")

            except Exception as e:
                logging.error(f"Error restoring individual invitation view {invitation_id}: {e}")
                failed_count += 1

        logging.info(f"Gang invitation view restoration completed: {restored_count} restored, {failed_count} failed")

        if failed_count > 0:
            logging.warning(f"Some gang invitation views failed to restore. Consider cleaning up invalid invitations.")

    except Exception as e:
        logging.error(f"Critical error in restore_gang_invitation_views: {e}")
        import traceback
        traceback.print_exc()

async def cleanup_expired_gang_invitations():
    """Clean up expired or invalid gang invitations"""
    try:
        current_time = datetime.now()
        expired_invitations = []

        for invitation_id, invitation_data in gang_invitations.items():
            try:
                # Check if invitation is older than 7 days
                created_at = datetime.fromisoformat(invitation_data.get("created_at", ""))
                age = current_time - created_at

                if age.days > 7:
                    expired_invitations.append(invitation_id)
                    logging.info(f"Marking invitation {invitation_id} for cleanup (age: {age.days} days)")

            except (ValueError, TypeError) as e:
                # Invalid date format or missing created_at
                expired_invitations.append(invitation_id)
                logging.warning(f"Marking invitation {invitation_id} for cleanup due to invalid date: {e}")

        # Remove expired invitations
        for invitation_id in expired_invitations:
            del gang_invitations[invitation_id]
            logging.info(f"Cleaned up expired invitation: {invitation_id}")

        if expired_invitations:
            await save_data()
            logging.info(f"Cleaned up {len(expired_invitations)} expired gang invitations")
        else:
            logging.info("No expired gang invitations found")

    except Exception as e:
        logging.error(f"Error cleaning up expired gang invitations: {e}")

async def force_restore_gang_invitation_views():
    """Force restore gang invitation views - useful for debugging"""
    try:
        logging.info("Force restoring gang invitation views...")

        # Clear existing views first
        for view in bot.persistent_views:
            if isinstance(view, GangInvitationView):
                bot.remove_view(view)

        # Restore all views
        await restore_gang_invitation_views()
        logging.info("Force restore completed")

    except Exception as e:
        logging.error(f"Error in force restore: {e}")

@tree.command(name="performance_stats", description="View bot performance statistics (Admin only)")
@app_commands.default_permissions(administrator=True)
async def performance_stats(interaction: discord.Interaction):
    """Display comprehensive performance statistics"""
    if not log_permission_check(interaction, "performance_stats"):
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        log_command_execution(interaction, "performance_stats", False)
        return

    log_command_execution(interaction, "performance_stats")

    try:
        await interaction.response.defer(ephemeral=True)

        # Create performance stats embed
        stats_embed = discord.Embed(
            title="🚀 Bot Performance Statistics",
            description="Comprehensive system performance metrics",
            color=0x00ff00
        )

        if PERFORMANCE_OPTIMIZATIONS_ENABLED:
            try:
                # Get performance manager stats
                performance_manager = get_performance_manager()
                perf_metrics = performance_manager.get_metrics()

                # Get enhanced database stats
                enhanced_db = get_enhanced_db_manager()
                db_stats = enhanced_db.get_performance_stats()

                # Get Discord API optimizer stats
                api_optimizer = get_discord_api_optimizer()
                api_stats = api_optimizer.get_comprehensive_stats()
            except Exception as e:
                logging.error(f"Error getting optimization stats: {e}")
                # Fallback to basic stats
                perf_metrics = {'total_requests': 0, 'success_rate': 100.0, 'avg_response_time': 0.0,
                               'peak_memory_usage_mb': 0.0, 'current_memory_mb': 0.0, 'queue_size': 0,
                               'active_workers': 0, 'processed_operations': 0, 'failed_operations': 0}
                db_stats = {'connection_pool': {'is_connected': False, 'query_count': 0, 'avg_query_time_ms': 0.0},
                           'query_cache': {'hit_rate': 0.0, 'size': 0, 'max_size': 0}}
                api_stats = {'rate_limiter': {'requests_made': 0, 'requests_blocked': 0},
                            'bulk_manager': {'operations_batched': 0, 'api_calls_saved': 0,
                                           'pending_message_batches': 0, 'pending_role_batches': 0}}

            # Performance metrics
            stats_embed.add_field(
                name="**📊 Performance Metrics**",
                value=f"```Total Requests: {perf_metrics['total_requests']:,}\n"
                      f"Success Rate: {perf_metrics['success_rate']:.1f}%\n"
                      f"Avg Response Time: {perf_metrics['avg_response_time']:.3f}s\n"
                      f"Peak Memory: {perf_metrics['peak_memory_usage_mb']:.1f}MB\n"
                      f"Current Memory: {perf_metrics['current_memory_mb']:.1f}MB```",
                inline=False
            )

            # Database performance
            db_conn_stats = db_stats['connection_pool']
            db_cache_stats = db_stats['query_cache']

            stats_embed.add_field(
                name="**🗄️ Database Performance**",
                value=f"```Connection Status: {'✅ Connected' if db_conn_stats['is_connected'] else '❌ Disconnected'}\n"
                      f"Query Count: {db_conn_stats['query_count']:,}\n"
                      f"Avg Query Time: {db_conn_stats['avg_query_time_ms']:.2f}ms\n"
                      f"Cache Hit Rate: {db_cache_stats['hit_rate']:.1f}%\n"
                      f"Cache Size: {db_cache_stats['size']:,}/{db_cache_stats['max_size']:,}```",
                inline=False
            )

            # API optimization
            rate_stats = api_stats['rate_limiter']
            bulk_stats = api_stats['bulk_manager']

            stats_embed.add_field(
                name="**🔄 API Optimization**",
                value=f"```Requests Made: {rate_stats['requests_made']:,}\n"
                      f"Requests Blocked: {rate_stats['requests_blocked']:,}\n"
                      f"Operations Batched: {bulk_stats['operations_batched']:,}\n"
                      f"API Calls Saved: {bulk_stats['api_calls_saved']:,}\n"
                      f"Active Batches: {bulk_stats['pending_message_batches'] + bulk_stats['pending_role_batches']}```",
                inline=False
            )

            # Queue status
            stats_embed.add_field(
                name="**⚡ Queue Status**",
                value=f"```Queue Size: {perf_metrics['queue_size']}\n"
                      f"Active Workers: {perf_metrics['active_workers']}\n"
                      f"Processed Ops: {perf_metrics['processed_operations']:,}\n"
                      f"Failed Ops: {perf_metrics['failed_operations']:,}```",
                inline=False
            )

        else:
            # Basic stats when optimizations are disabled
            import psutil
            process = psutil.Process()
            memory_info = process.memory_info()

            stats_embed.add_field(
                name="**📊 Basic Metrics**",
                value=f"```Memory Usage: {memory_info.rss / 1024 / 1024:.1f}MB\n"
                      f"CPU Usage: {process.cpu_percent():.1f}%\n"
                      f"Optimization: ❌ Disabled```",
                inline=False
            )

            stats_embed.add_field(
                name="**⚠️ Performance Notice**",
                value="Performance optimizations are not enabled. Consider installing the required dependencies for enhanced performance.",
                inline=False
            )

        # Memory manager stats
        try:
            memory_manager = get_memory_manager()
            memory_stats = memory_manager.get_stats()

            stats_embed.add_field(
                name="**🧠 Memory Management**",
                value=f"```Current Usage: {memory_stats['current_memory_mb']:.1f}MB\n"
                      f"Peak Usage: {memory_stats['peak_memory_mb']:.1f}MB\n"
                      f"GC Collections: {memory_stats['gc_collections']:,}\n"
                      f"Objects Cleaned: {memory_stats['objects_cleaned']:,}```",
                inline=False
            )
        except Exception as e:
            logging.error(f"Error getting memory stats: {e}")
            # Fallback memory stats
            try:
                import psutil
                process = psutil.Process()
                memory_info = process.memory_info()
                memory_mb = memory_info.rss / 1024 / 1024

                stats_embed.add_field(
                    name="**🧠 Memory Management**",
                    value=f"```Current Usage: {memory_mb:.1f}MB\n"
                          f"Peak Usage: {memory_mb:.1f}MB\n"
                          f"GC Collections: N/A\n"
                          f"Objects Cleaned: N/A```",
                    inline=False
                )
            except Exception:
                stats_embed.add_field(
                    name="**🧠 Memory Management**",
                    value="```Memory stats unavailable```",
                    inline=False
                )

        current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
        stats_embed.set_footer(text=f"Performance Monitor • {current_time}")

        await interaction.followup.send(embed=stats_embed, ephemeral=True)

    except Exception as e:
        logging.error(f"Error in performance_stats: {e}")
        await interaction.followup.send(f"An error occurred while retrieving performance stats: {str(e)}", ephemeral=True)

async def send_targeted_member_notification(gang_name, notification_type, interaction, target_member=None, **kwargs):
    """Send targeted notifications for member management actions"""
    if gang_name not in gang_roles:
        return

    gang_data = gang_roles[gang_name]
    actor_id = interaction.user.id

    # Create different embeds for different recipients
    if notification_type == "member_added":
        # Notification for the person performing the action (confirmation)
        actor_embed = await create_gang_notification_embed(
            title="Member Addition Confirmed",
            description=f"You have successfully added **{target_member.display_name}** to gang **{gang_name}**.",
            gang_name=gang_name,
            notification_type="action_confirmation",
            action="Member Addition",
            target=target_member.display_name,
            current_count=gang_data['current_members'],
            member_limit=gang_data['member_limit']
        )

        # Notification for the person being added
        target_embed = await create_gang_notification_embed(
            title="Welcome to the Gang",
            description=f"You have been added to gang **{gang_name}**! Welcome to the team.",
            gang_name=gang_name,
            notification_type="membership_change",
            action="Added to Gang",
            added_by=interaction.user.display_name,
            current_count=gang_data['current_members'],
            member_limit=gang_data['member_limit']
        )

        # Note: No longer sending confirmation DM to the person performing the action
        # This is handled by the command response embed instead

        # Send welcome notification to the new member
        try:
            await target_member.send(embed=target_embed)
        except Exception as e:
            logging.warning(f"Could not notify new member {target_member.id}: {e}")

        # Notify leader only if they are not the person performing the action
        leader_id = gang_data["leader"]
        if leader_id != actor_id:
            leader = interaction.guild.get_member(leader_id)
            if leader:
                leader_embed = await create_gang_notification_embed(
                    title="Gang Member Added",
                    description=f"**{target_member.display_name}** has been added to your gang **{gang_name}** by **{interaction.user.display_name}**.",
                    gang_name=gang_name,
                    notification_type="leadership_notification",
                    action="Member Addition",
                    target=target_member.display_name,
                    performed_by=interaction.user.display_name,
                    current_count=gang_data['current_members'],
                    member_limit=gang_data['member_limit']
                )
                try:
                    await leader.send(embed=leader_embed)
                except Exception as e:
                    logging.warning(f"Could not notify gang leader {leader_id}: {e}")

    elif notification_type == "member_removed":
        # Notification for the person performing the action (confirmation)
        actor_embed = await create_gang_notification_embed(
            title="Member Removal Confirmed",
            description=f"You have successfully removed **{target_member.display_name}** from gang **{gang_name}**.",
            gang_name=gang_name,
            notification_type="action_confirmation",
            action="Member Removal",
            target=target_member.display_name,
            current_count=gang_data['current_members'],
            member_limit=gang_data['member_limit']
        )

        # Notification for the person being removed
        target_embed = await create_gang_notification_embed(
            title="Gang Membership Ended",
            description=f"You have been removed from gang **{gang_name}**.",
            gang_name=gang_name,
            notification_type="membership_change",
            action="Removed from Gang",
            removed_by=interaction.user.display_name,
            current_count=gang_data['current_members'],
            member_limit=gang_data['member_limit']
        )

        # Note: No longer sending confirmation DM to the person performing the action
        # This is handled by the command response embed instead

        # Send removal notification to the former member
        try:
            await target_member.send(embed=target_embed)
        except Exception as e:
            logging.warning(f"Could not notify removed member {target_member.id}: {e}")

        # Notify leader only if they are not the person performing the action
        leader_id = gang_data["leader"]
        if leader_id != actor_id:
            leader = interaction.guild.get_member(leader_id)
            if leader:
                leader_embed = await create_gang_notification_embed(
                    title="Gang Member Removed",
                    description=f"**{target_member.display_name}** has been removed from your gang **{gang_name}** by **{interaction.user.display_name}**.",
                    gang_name=gang_name,
                    notification_type="leadership_notification",
                    action="Member Removal",
                    target=target_member.display_name,
                    performed_by=interaction.user.display_name,
                    current_count=gang_data['current_members'],
                    member_limit=gang_data['member_limit']
                )
                try:
                    await leader.send(embed=leader_embed)
                except Exception as e:
                    logging.warning(f"Could not notify gang leader {leader_id}: {e}")

async def notify_gang_members_with_embed(gang_name, embed, interaction):
    """Send professional embed notifications to all gang members (for non-member management actions)"""
    if gang_name not in gang_roles:
        return

    # Notify leader
    leader_id = gang_roles[gang_name]["leader"]
    leader = interaction.guild.get_member(leader_id)
    if leader:
        try:
            await leader.send(embed=embed)
        except Exception as e:
            logging.warning(f"Could not notify gang leader {leader.id}: {e}")

    # Notify members
    for member_id in gang_roles[gang_name]["members"]:
        if isinstance(member_id, int):
            member = interaction.guild.get_member(member_id)
            if member:
                try:
                    await member.send(embed=embed)
                except Exception as e:
                    logging.warning(f"Could not notify gang member {member.id}: {e}")

async def notify_gang_changes(gang_name, changes, interaction):
    """Notify gang members about changes using professional embeds"""
    embed = await create_gang_notification_embed(
        title="Gang Settings Updated",
        description=f"Your gang **{gang_name}** has been updated by server administration.",
        gang_name=gang_name,
        notification_type="settings_changed",
        changes=changes
    )
    await notify_gang_members_with_embed(gang_name, embed, interaction)

def verify_member_count(gang_name):
    """Verify and correct member count"""
    actual_members = len([m for m in gang_roles[gang_name]["members"] if isinstance(m, int)])
    gang_roles[gang_name]["current_members"] = actual_members
    return actual_members

def check_bot_permissions(guild):
    """Check if bot has required permissions"""
    bot_member = guild.get_member(bot.user.id)
    required_permissions = ["manage_roles", "send_messages", "embed_links"]
    return [perm for perm in required_permissions if not getattr(bot_member.guild_permissions, perm)]

@tree.command(name="issue_strike", description="Issue a strike to a gang")
@app_commands.default_permissions(administrator=True)
async def issue_strike(interaction: discord.Interaction, gang_name: str, reason: str):
    try:
        # Defer the response immediately to prevent timeout
        await interaction.response.defer()

        if gang_name not in gang_roles:
            # Create professional error embed for gang not found
            error_embed = discord.Embed(
                title="❌ Gang Not Found",
                description=f"Gang **{gang_name}** does not exist in the system.",
                color=0x000000  # Professional black theme
            )

            error_embed.add_field(
                name="**Error Details**",
                value=f"```Requested Gang: {gang_name}\nStatus: Not Found\nAction: Strike issuance failed```",
                inline=False
            )

            error_embed.add_field(
                name="**💡 Suggestion**",
                value="Use `/check_gang_list` to view all existing gangs in the system.",
                inline=False
            )

            current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
            error_embed.set_footer(text=f"Gang Management System • {current_time}")

            await interaction.followup.send(embed=error_embed)
            return

        # Initialize strikes for the gang if not exists
        if gang_name not in gang_strikes:
            gang_strikes[gang_name] = 0

        # Add the strike
        gang_strikes[gang_name] += 1
        await save_data()

        # Create professional strike notification embed
        embed = await create_gang_notification_embed(
            title="Strike Issued",
            description=f"Your gang **{gang_name}** has received a disciplinary strike.",
            gang_name=gang_name,
            notification_type="strike_added",
            current_strikes=gang_strikes[gang_name],
            reason=reason
        )

        # Send professional embed notifications to all gang members
        await notify_gang_members_with_embed(gang_name, embed, interaction)

        # Create professional response embed for the administrator
        response_embed = discord.Embed(
            title="⚠️ Strike Issued Successfully",
            description=f"Disciplinary strike issued to gang **{gang_name}**.",
            color=0x000000  # Professional black theme
        )

        response_embed.add_field(
            name="**Strike Details**",
            value=f"```Gang: {gang_name}\nCurrent Strikes: {gang_strikes[gang_name]}\nReason: {reason}```",
            inline=False
        )

        response_embed.add_field(
            name="**⚠️ Notification Status**",
            value="All gang members have been notified of this disciplinary action.",
            inline=False
        )

        current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
        response_embed.set_footer(text=f"Gang Management System • {current_time}")

        await interaction.followup.send(embed=response_embed)

    except Exception as e:
        print(f"Error in issue_strike: {e}")
        try:
            if not interaction.response.is_done():
                await interaction.response.send_message("An error occurred while processing your request.", ephemeral=True)
        except:
            pass

# Updated edit_gang command
@tree.command(name="edit_gang", description="Edit gang settings")
@app_commands.default_permissions(administrator=True)
async def edit_gang(interaction: discord.Interaction, gang_name: str, new_member_limit: int = None, new_leader: discord.Member = None):
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        return
    try:
        await interaction.response.defer()

        # Initial validations
        if gang_name not in gang_roles:
            await interaction.followup.send("Gang not found.", ephemeral=True)
            return

        # Check bot permissions
        missing_permissions = check_bot_permissions(interaction.guild)
        if missing_permissions:
            await interaction.followup.send(
                f"Bot is missing required permissions: {', '.join(missing_permissions)}",
                ephemeral=True
            )
            return

        # Create backup before making changes
        await create_backup()

        # Store original data for rollback
        original_data = {
            "gang_roles": gang_roles.copy(),
            "gang_leaders": gang_leaders.copy()
        }

        try:
            changes = []
            if new_member_limit is not None:
                if new_member_limit <= 0:
                    await interaction.followup.send("Member limit must be greater than 0", ephemeral=True)
                    return

                if new_member_limit < gang_roles[gang_name]["current_members"]:
                    await interaction.followup.send(
                        "New member limit cannot be less than current member count.",
                        ephemeral=True
                    )
                    return

                old_limit = gang_roles[gang_name]["member_limit"]
                gang_roles[gang_name]["member_limit"] = new_member_limit
                changes.append(f"Member limit changed from {old_limit} to {new_member_limit}")

            if new_leader:
                # Check if new leader is already leading another gang
                for other_gang, details in gang_roles.items():
                    if details["leader"] == new_leader.id and other_gang != gang_name:
                        await interaction.followup.send(
                            "This user is already a leader of another gang.",
                            ephemeral=True
                        )
                        return

                old_leader_id = gang_roles[gang_name]["leader"]
                old_leader = interaction.guild.get_member(old_leader_id)

                # Remove new leader from members list if they're there
                if new_leader.id in gang_roles[gang_name]["members"]:
                    gang_roles[gang_name]["members"].remove(new_leader.id)
                    gang_roles[gang_name]["current_members"] -= 1

                # Update gang data
                gang_roles[gang_name]["leader"] = new_leader.id
                gang_leaders[new_leader.id] = gang_roles[gang_name]["leader_role"]

                # Remove old leader's data
                if old_leader_id in gang_leaders:
                    del gang_leaders[old_leader_id]

                # Update leader roles
                leader_role_id = gang_roles[gang_name]["leader_role"]
                leader_role = interaction.guild.get_role(leader_role_id)

                if leader_role:
                    # Handle old leader
                    if old_leader:
                        await old_leader.remove_roles(leader_role)
                        # Convert old leader to member if there's a member role
                        member_role_id = next((role_id for role_id in gang_roles[gang_name]["members"]
                                             if isinstance(role_id, int)), None)
                        if member_role_id:
                            member_role = interaction.guild.get_role(member_role_id)
                            if member_role:
                                await old_leader.add_roles(member_role)
                                gang_roles[gang_name]["members"].append(old_leader_id)
                                gang_roles[gang_name]["current_members"] += 1

                    # Assign new leader role
                    await new_leader.add_roles(leader_role)

                changes.append(f"Leader changed from {old_leader.mention if old_leader else 'Unknown'} to {new_leader.mention}")

            # Verify member count
            verify_member_count(gang_name)

            await save_data()

            # Create response embed
            embed = discord.Embed(
                title="Gang Settings Updated",
                description=f"Gang: {gang_name}",
                color=discord.Color.green()
            )

            for change in changes:
                embed.add_field(name="Change", value=change, inline=False)

            # Notify members about changes
            await notify_gang_changes(gang_name, changes, interaction)

            await interaction.followup.send(embed=embed)
            logging.info(f"Gang {gang_name} edited by {interaction.user}: {', '.join(changes)}")

        except Exception as e:
            # Rollback changes on error
            gang_roles.update(original_data["gang_roles"])
            gang_leaders.update(original_data["gang_leaders"])
            raise e

    except discord.Forbidden:
        logging.error(f"Permission error in edit_gang: {interaction.guild.id}")
        await interaction.followup.send("I don't have permission to manage roles.", ephemeral=True)
    except discord.HTTPException as e:
        logging.error(f"Discord API error in edit_gang: {e}")
        await interaction.followup.send(f"Discord API error: {str(e)}", ephemeral=True)
    except Exception as e:
        logging.error(f"Error editing gang: {e}")
        await interaction.followup.send("An error occurred while editing the gang.", ephemeral=True)

# Application System
@tree.command(name="set_application_channel", description="Set the application channel")
@app_commands.default_permissions(administrator=True)
async def set_application_channel(interaction: discord.Interaction, channel: discord.TextChannel):
    global application_channel_id
    application_channel_id = channel.id
    await save_data_optimized()  # Save data after setting the welcome channel
    await interaction.response.send_message(f"Application channel set to {channel.mention}")
    await send_application_embed(channel)

@tree.command(name="set_application_log_channel", description="Set the application log channel")
@app_commands.default_permissions(administrator=True)
async def set_application_log_channel(interaction: discord.Interaction, channel: discord.TextChannel):
    global application_log_channel
    application_log_channel = channel.id
    await save_data()  # Save data after setting the welcome channel
    await interaction.response.send_message(f"Application log channel set to {channel.mention}")

@tree.command(name="create_application", description="Create a new application form")
@app_commands.default_permissions(administrator=True)
async def create_application(interaction: discord.Interaction):
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        return
    class ApplicationModal(Modal):
        def __init__(self):
            super().__init__(title="Create Application")

            self.add_item(TextInput(label="Application Name", placeholder="Enter the application name"))
            self.add_item(TextInput(label="Questions", placeholder="Enter questions separated by semicolons", style=discord.TextStyle.long))

        async def on_submit(self, interaction: discord.Interaction):
            name = self.children[0].value
            questions = self.children[1].value.split(';')
            application_forms[name] = [q.strip() for q in questions if q.strip()]
            await save_data_optimized()
            await interaction.response.send_message(f"Application '{name}' created with {len(application_forms[name])} questions.")

    await interaction.response.send_modal(ApplicationModal())

@tree.command(name="list_applications", description="List all applications with questions")
@app_commands.default_permissions(administrator=True)
async def list_applications(interaction: discord.Interaction):
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        return
    if not application_forms:
        await interaction.response.send_message("No applications found.")
        return

    embed = discord.Embed(title="Application List", color=discord.Color.blue())
    for name, questions in application_forms.items():
        embed.add_field(name=name, value="\n".join(questions), inline=False)

    await interaction.response.send_message(embed=embed)

@tree.command(name="edit_application", description="Edit an existing application form")
@app_commands.default_permissions(administrator=True)
async def edit_application(interaction: discord.Interaction, name: str):
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        return
    log_permission_check(interaction, "edit_application")
    if name not in application_forms:
        await interaction.response.send_message(f"Application '{name}' not found.")
        return

    modal = EditApplicationModal(name)
    await interaction.response.send_modal(modal)

class EditApplicationModal(discord.ui.Modal):
    def __init__(self, name):
        super().__init__(title="Edit Application")
        self.old_name = name
        self.add_item(discord.ui.TextInput(label="Application Name", default=name))
        self.add_item(discord.ui.TextInput(label="Questions", default="; ".join(application_forms[name]), style=discord.TextStyle.long))

    async def on_submit(self, interaction: discord.Interaction):
        new_name = self.children[0].value
        questions = self.children[1].value.split(';')
        if new_name != self.old_name:
            application_forms[new_name] = application_forms.pop(self.old_name)
        application_forms[new_name] = [q.strip() for q in questions if q.strip()]
        await save_data()
        await interaction.response.send_message(f"Application '{new_name}' updated with {len(application_forms[new_name])} questions.")


@tree.command(name="remove_application", description="Remove an existing application form")
@app_commands.default_permissions(administrator=True)
async def remove_application(interaction: discord.Interaction, name: str):
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        return
    if name in application_forms:
        del application_forms[name]
        await save_data()  # Save data after setting the welcome channel
        await interaction.response.send_message(f"Application '{name}' has been removed.")
    else:
        await interaction.response.send_message(f"Application '{name}' does not exist.")

async def send_application_embed(channel):
    # Create options with professional icons for each application form
    options = [
        discord.SelectOption(
            label=name,
            value=name,
            emoji="📋",
            description=f"Apply for {name} position"
        ) for name in application_forms.keys()
    ]

    # Create a dropdown with a professional placeholder
    select = Select(placeholder="Select an application type", options=options)

    async def select_callback(interaction):
        selected_application = select.values[0]

        # Create a professional-looking embed for application confirmation
        embed = discord.Embed(
            title="Application Confirmation",
            description=f"You've selected the **{selected_application}** application.\n\nBefore proceeding, please note:\n• This application will take approximately 5-10 minutes to complete\n• You have 60 minutes to submit your responses\n• All information provided will be reviewed by our staff team",
            color=0x2b2d31  # Professional dark theme color
        )

        # Add a clean divider
        embed.add_field(name="", value="━━━━━━━━━━━━━━━━━━━━━━━", inline=False)

        # Add application instructions
        embed.add_field(
            name="Instructions",
            value="• Answer all questions honestly and thoroughly\n• Provide specific examples when possible\n• Review your answers before submitting",
            inline=False
        )

        # Add professional footer and thumbnail
        if interaction.guild.icon:
            embed.set_thumbnail(url=interaction.guild.icon.url)

        embed.set_footer(text=f"© {datetime.now().year} Application System • Confidential")

        # Create professional-looking buttons
        view = View()
        confirm_button = Button(label="Begin Application", style=discord.ButtonStyle.primary)
        cancel_button = Button(label="Cancel", style=discord.ButtonStyle.secondary)

        async def confirm_callback(interaction):
            await interaction.response.send_message("Your application has been initiated. Please check your DMs to complete the process.")
            await start_application(interaction.user, selected_application)

        async def cancel_callback(interaction):
            await interaction.response.send_message("Application process canceled.", ephemeral=True)

        confirm_button.callback = confirm_callback
        cancel_button.callback = cancel_callback
        view.add_item(confirm_button)
        view.add_item(cancel_button)

        await interaction.user.send(embed=embed, view=view)

    select.callback = select_callback

    # Create a professional-looking main embed for the application portal
    embed = discord.Embed(
        title="Application Portal",
        description="Welcome to our official application system. Select an application type below to begin the process.",
        color=0x2b2d31  # Professional dark theme color
    )

    # Add information about the application process
    embed.add_field(
        name="Application Process",
        value="1. Select an application type below\n2. Complete the application form\n3. Submit your responses\n4. Wait for staff review",
        inline=False
    )

    # Add a note about privacy
    embed.add_field(
        name="Privacy Notice",
        value="All information provided will be kept confidential and reviewed only by authorized staff members.",
        inline=False
    )

    # Add professional footer and timestamp
    embed.set_footer(text=f"© {datetime.now().year} Application System")
    embed.timestamp = datetime.now()

    # Add guild icon if available
    if channel.guild.icon:
        embed.set_thumbnail(url=channel.guild.icon.url)

    # Create a view with the dropdown
    view = View()
    view.add_item(select)

    # Send the embed with the dropdown menu
    await channel.send(embed=embed, view=view)

async def start_application(user, application_name):
    """
    Handles the application process for the user with a professional interface.
    """
    try:
        # Ensure application_forms is accessible
        if not application_forms:
            error_embed = discord.Embed(
                title="Application Error",
                description="No application forms are currently available. Please contact an administrator for assistance.",
                color=0xE74C3C  # Professional red color
            )
            error_embed.set_footer(text="Application System • Error")
            await user.send(embed=error_embed)
            return

        # Get the questions for the selected application
        questions = application_forms.get(application_name)
        if not questions:
            error_embed = discord.Embed(
                title="Application Error",
                description=f"The application '{application_name}' could not be found. Please contact an administrator for assistance.",
                color=0xE74C3C  # Professional red color
            )
            error_embed.set_footer(text="Application System • Error")
            await user.send(embed=error_embed)
            return

        answers = []

        # Send professional initial application start message
        embed = discord.Embed(
            title=f"{application_name} Application",
            description="Your application process has begun. Please answer each question thoroughly and professionally.",
            color=0x2b2d31  # Professional dark theme color
        )

        # Add application guidelines
        embed.add_field(
            name="Guidelines",
            value="• You have 60 minutes to complete this application\n• Answer each question as it appears\n• Be honest and detailed in your responses\n• You can cancel the application at any time",
            inline=False
        )

        # Add a divider
        embed.add_field(name="", value="━━━━━━━━━━━━━━━━━━━━━━━", inline=False)

        # Add information about the process
        embed.add_field(
            name="Process",
            value=f"• Total questions: **{len(questions)}**\n• Each question will appear one at a time\n• Type your answer and send it as a message",
            inline=False
        )

        embed.set_footer(text=f"© {datetime.now().year} Application System • Confidential")
        embed.timestamp = datetime.now()

        # Create a professional view with cancel button
        view = View()
        cancel_button = Button(label="Cancel Application", style=discord.ButtonStyle.secondary)

        async def cancel_callback(interaction):
            cancel_embed = discord.Embed(
                title="Application Canceled",
                description="Your application has been canceled. You can restart the process at any time by returning to the application portal.",
                color=0x95A5A6  # Professional gray color
            )
            cancel_embed.set_footer(text="Application System")
            await interaction.response.send_message(embed=cancel_embed)
            return

        cancel_button.callback = cancel_callback
        view.add_item(cancel_button)
        await user.send(embed=embed, view=view)

        # Send a starting message
        start_embed = discord.Embed(
            title="Application Started",
            description="Your first question will appear momentarily. Please be ready to respond.",
            color=0x2b2d31
        )
        await user.send(embed=start_embed)

        # Brief delay before first question
        await asyncio.sleep(2)

        # Ask questions one by one with professional formatting
        for i, question in enumerate(questions):
            question_embed = discord.Embed(
                title=f"Question {i+1} of {len(questions)}",
                description=question,
                color=0x3498DB  # Professional blue color
            )
            question_embed.set_footer(text=f"Type your answer below • {i+1}/{len(questions)}")
            await user.send(embed=question_embed)

            try:
                answer = await bot.wait_for('message', timeout=60.0, check=lambda m: m.author == user and m.channel.type == discord.ChannelType.private)
                answers.append(answer.content)

                # Send confirmation of answer received
                if i < len(questions) - 1:  # Don't send for the last question
                    confirm_embed = discord.Embed(
                        description=f"✓ Answer recorded. Next question coming up...",
                        color=0x2ECC71  # Professional green color
                    )
                    await user.send(embed=confirm_embed)
                    await asyncio.sleep(1)  # Brief pause between questions

            except asyncio.TimeoutError:
                timeout_embed = discord.Embed(
                    title="Application Timeout",
                    description="You took too long to answer. Your application has been canceled. You can restart the process at any time.",
                    color=0xE74C3C  # Professional red color
                )
                timeout_embed.set_footer(text="Application System • Timeout")
                await user.send(embed=timeout_embed)
                return

        # Send professional completion message to the user
        complete_embed = discord.Embed(
            title="Application Submitted",
            description="Thank you for completing your application. Your responses have been recorded and will be reviewed by our staff team.",
            color=0x2ECC71  # Professional green color
        )
        complete_embed.add_field(
            name="Next Steps",
            value="• Your application will be reviewed by our staff team\n• You will be notified of the decision\n• This process typically takes 1-3 days",
            inline=False
        )
        complete_embed.set_footer(text=f"© {datetime.now().year} Application System • Application ID: {user.id}")
        complete_embed.timestamp = datetime.now()

        await user.send(embed=complete_embed)

        # Log the application
        await log_application(user, application_name, questions, answers, application_log_channel)

    except Exception as e:
        logging.error(f"Error in start_application: {e}")
        error_embed = discord.Embed(
            title="Application Error",
            description="An error occurred while processing your application. Please try again later or contact an administrator for assistance.",
            color=0xE74C3C  # Professional red color
        )
        error_embed.set_footer(text="Application System • Error")
        await user.send(embed=error_embed)

        # Log the application
async def log_application(user, application_name, questions, answers, application_log_channel):
    """
    Logs the application details in the specified log channel with a professional format.
    """
    try:
        if not application_log_channel:
            logging.error("Application log channel not set.")
            return

        log_channel = bot.get_channel(application_log_channel)
        if not log_channel:
            logging.error(f"Log channel with ID {application_log_channel} not found.")
            return

        submission_time = datetime.now(timezone.utc)
        user_id_str = str(user.id)

        # Create a professional embed for the application log
        embed = discord.Embed(
            title=f"New Application Submission",
            description=f"A new application has been submitted and requires review.",
            color=0x2b2d31  # Professional dark theme color
        )

        # Add applicant information section
        embed.add_field(
            name="Applicant Information",
            value=f"**User:** {user.mention} ({user.name}#{user.discriminator})\n"
                  f"**User ID:** {user.id}\n"
                  f"**Application Type:** {application_name}\n"
                  f"**Submitted:** {submission_time.strftime('%Y-%m-%d %H:%M:%S')} UTC",
            inline=False
        )

        # Add a divider
        embed.add_field(name="", value="━━━━━━━━━━━━━━━━━━━━━━━", inline=False)

        # Add application responses section
        embed.add_field(
            name="Application Responses",
            value="Below are the questions and answers provided by the applicant:",
            inline=False
        )

        # Add each question and answer with proper formatting
        for i, (question, answer) in enumerate(zip(questions, answers)):
            # Add question with number
            embed.add_field(
                name=f"Question {i+1}",
                value=f"{question}",
                inline=False
            )

            # Add answer with proper formatting
            # Truncate very long answers to prevent embed issues
            if len(answer) > 1000:
                answer_display = answer[:997] + "..."
            else:
                answer_display = answer

            embed.add_field(
                name=f"Answer {i+1}",
                value=f"```{answer_display}```",
                inline=False
            )

        # Add a divider before actions section
        embed.add_field(name="", value="━━━━━━━━━━━━━━━━━━━━━━━", inline=False)

        # Add instructions for staff
        embed.add_field(
            name="Staff Actions",
            value="Please review this application and select an appropriate action below.",
            inline=False
        )

        # Set footer with application ID and timestamp
        embed.set_footer(text=f"Application ID: {user.id} • Submitted: {submission_time.strftime('%Y-%m-%d %H:%M:%S')} UTC")
        embed.timestamp = submission_time

        # Add user avatar if available
        if user.avatar:
            embed.set_thumbnail(url=user.avatar.url)

        # Create professional buttons for response with persistent custom IDs
        view = View(timeout=None)  # Set timeout to None for persistent view

        # Create buttons with custom_ids to make them persistent
        accept_button = Button(
            label="Approve Application",
            style=discord.ButtonStyle.success,
            custom_id=f"app_accept_{user_id_str}"
        )

        reject_button = Button(
            label="Decline Application",
            style=discord.ButtonStyle.danger,
            custom_id=f"app_reject_{user_id_str}"
        )

        accept_reason_button = Button(
            label="Approve with Feedback",
            style=discord.ButtonStyle.primary,
            custom_id=f"app_accept_reason_{user_id_str}"
        )

        reject_reason_button = Button(
            label="Decline with Feedback",
            style=discord.ButtonStyle.secondary,
            custom_id=f"app_reject_reason_{user_id_str}"
        )

        # Add Create Ticket button for staff follow-up
        create_ticket_button = Button(
            label="Create Ticket",
            style=discord.ButtonStyle.primary,
            custom_id=f"app_create_ticket_{user_id_str}",
            emoji="🎫"
        )

        # Define callbacks for the buttons
        async def accept_callback(interaction):
            await handle_application_response(interaction, user, "accepted", application_name)

        async def reject_callback(interaction):
            await handle_application_response(interaction, user, "rejected", application_name)

        async def accept_with_reason_callback(interaction):
            if applications_status.get(user_id_str, {}).get("responded"):
                await interaction.response.send_message("This application has already been processed.", ephemeral=True)
                return
            await interaction.response.send_modal(AcceptReasonModal(user, application_name))

        async def reject_with_reason_callback(interaction):
            if applications_status.get(user_id_str, {}).get("responded"):
                await interaction.response.send_message("This application has already been processed.", ephemeral=True)
                return
            await interaction.response.send_modal(RejectReasonModal(user, application_name))

        # Assign callbacks to buttons (Create Ticket handled in main on_interaction event)
        accept_button.callback = accept_callback
        reject_button.callback = reject_callback
        accept_reason_button.callback = accept_with_reason_callback
        reject_reason_button.callback = reject_with_reason_callback
        # Note: create_ticket_button callback is handled in the main on_interaction event

        # Add buttons to view
        view.add_item(accept_button)
        view.add_item(reject_button)
        view.add_item(accept_reason_button)
        view.add_item(reject_reason_button)
        view.add_item(create_ticket_button)

        # Send the embed with buttons to the log channel
        log_message = await log_channel.send(embed=embed, view=view)

        # Store the message ID with the application status
        applications_status[user_id_str] = {
            "responded": False,
            "message_id": log_message.id,
            "application_name": application_name,
            "submission_time": submission_time.isoformat(),
            "questions": questions,
            "answers": answers
        }
        await save_data()
        logging.info(f"Application logged for user {user_id_str} with message ID {log_message.id}")

    except Exception as e:
        logging.error(f"Error in log_application: {e}")
        error_embed = discord.Embed(
            title="Application Error",
            description="An error occurred while processing your application. Our staff has been notified of this issue.",
            color=0xE74C3C  # Professional red color
        )
        error_embed.set_footer(text="Application System • Error")
        await user.send(embed=error_embed)

class AcceptReasonModal(discord.ui.Modal):
    def __init__(self, user, application_name):
        super().__init__(title="Approve Application with Feedback")
        self.user = user
        self.application_name = application_name

        # Add professional text inputs
        self.add_item(discord.ui.TextInput(
            label="Feedback for Applicant",
            placeholder="Provide constructive feedback for the applicant...",
            style=discord.TextStyle.paragraph,
            min_length=10,
            max_length=1000,
            required=True
        ))

        # Add optional internal notes field
        self.add_item(discord.ui.TextInput(
            label="Internal Notes (Staff Only)",
            placeholder="Optional notes for staff records (not sent to applicant)",
            style=discord.TextStyle.paragraph,
            required=False
        ))

    async def on_submit(self, interaction):
        try:
            feedback = self.children[0].value
            internal_notes = self.children[1].value if len(self.children) > 1 else ""
            user_id_str = str(self.user.id)
            current_time = datetime.now(timezone.utc)

            # Check if application has already been responded to
            if applications_status.get(user_id_str, {}).get("responded"):
                try:
                    await interaction.response.send_message("This application has already been processed.", ephemeral=True)
                except discord.errors.InteractionResponded:
                    await interaction.followup.send("This application has already been processed.", ephemeral=True)
                return

            # Update application status with detailed information
            applications_status[user_id_str] = {
                "responded": True,
                "admin": interaction.user.id,
                "admin_name": f"{interaction.user.name}#{interaction.user.discriminator}",
                "status": "accepted",
                "feedback": feedback,
                "internal_notes": internal_notes,
                "response_time": current_time.isoformat()
            }
            await save_data()

            # Send professional response to admin
            admin_embed = discord.Embed(
                title="Application Approved",
                description=f"You have approved the application for {self.user.mention}.",
                color=0x2ECC71  # Professional green color
            )
            admin_embed.add_field(name="Application Type", value=self.application_name, inline=False)
            admin_embed.add_field(name="Feedback Provided", value=feedback, inline=False)
            if internal_notes:
                admin_embed.add_field(name="Internal Notes", value=internal_notes, inline=False)
            admin_embed.set_footer(text=f"Processed at {current_time.strftime('%Y-%m-%d %H:%M:%S')} UTC")

            try:
                await interaction.response.send_message(embed=admin_embed, ephemeral=True)
            except discord.errors.InteractionResponded:
                await interaction.followup.send(embed=admin_embed, ephemeral=True)

            # Send professional notification to applicant
            try:
                user_embed = discord.Embed(
                    title="Application Approved",
                    description=f"Congratulations! Your application for **{self.application_name}** has been approved.",
                    color=0x2ECC71  # Professional green color
                )

                # Add feedback section
                user_embed.add_field(
                    name="Feedback from Staff",
                    value=feedback,
                    inline=False
                )

                # Add next steps section
                user_embed.add_field(
                    name="Next Steps",
                    value="• Please read all feedback carefully\n• Follow any instructions provided\n• Contact staff if you have any questions",
                    inline=False
                )

                # Add footer with timestamp
                user_embed.set_footer(text=f"© {datetime.now().year} Application System")
                user_embed.timestamp = current_time

                await self.user.send(embed=user_embed)
                logging.info(f"Approval notification sent to user {self.user.id} for {self.application_name} application")
            except discord.errors.Forbidden:
                logging.warning(f"Could not send DM to user {self.user.id} - DMs may be disabled")
            except Exception as dm_error:
                logging.error(f"Error sending DM to user {self.user.id}: {dm_error}")

            logging.info(f"Application for user {user_id_str} approved with feedback by {interaction.user.id}")

        except discord.errors.InteractionResponded:
            logging.warning("Interaction was already responded to in AcceptReasonModal")
            try:
                await interaction.followup.send("Processing your response...", ephemeral=True)
            except:
                pass
        except Exception as e:
            logging.error(f"Error in AcceptReasonModal.on_submit: {e}")
            try:
                await interaction.response.send_message("An error occurred while processing your response.", ephemeral=True)
            except discord.errors.InteractionResponded:
                try:
                    await interaction.followup.send("An error occurred while processing your response.", ephemeral=True)
                except:
                    pass

class RejectReasonModal(discord.ui.Modal):
    def __init__(self, user, application_name):
        super().__init__(title="Decline Application with Feedback")
        self.user = user
        self.application_name = application_name

        # Add professional text inputs
        self.add_item(discord.ui.TextInput(
            label="Feedback for Applicant",
            placeholder="Provide constructive feedback explaining the decision...",
            style=discord.TextStyle.paragraph,
            min_length=10,
            max_length=1000,
            required=True
        ))

        # Add optional improvement suggestions field
        self.add_item(discord.ui.TextInput(
            label="Improvement Suggestions",
            placeholder="Suggestions for how the applicant could improve for future applications",
            style=discord.TextStyle.paragraph,
            required=False
        ))

        # Add optional internal notes field
        self.add_item(discord.ui.TextInput(
            label="Internal Notes (Staff Only)",
            placeholder="Optional notes for staff records (not sent to applicant)",
            style=discord.TextStyle.paragraph,
            required=False
        ))

    async def on_submit(self, interaction):
        try:
            feedback = self.children[0].value
            improvement = self.children[1].value if len(self.children) > 1 else ""
            internal_notes = self.children[2].value if len(self.children) > 2 else ""
            user_id_str = str(self.user.id)
            current_time = datetime.now(timezone.utc)

            # Check if application has already been responded to
            if applications_status.get(user_id_str, {}).get("responded"):
                try:
                    await interaction.response.send_message("This application has already been processed.", ephemeral=True)
                except discord.errors.InteractionResponded:
                    await interaction.followup.send("This application has already been processed.", ephemeral=True)
                return

            # Update application status with detailed information
            applications_status[user_id_str] = {
                "responded": True,
                "admin": interaction.user.id,
                "admin_name": f"{interaction.user.name}#{interaction.user.discriminator}",
                "status": "rejected",
                "feedback": feedback,
                "improvement": improvement,
                "internal_notes": internal_notes,
                "response_time": current_time.isoformat()
            }
            await save_data()

            # Send professional response to admin
            admin_embed = discord.Embed(
                title="Application Declined",
                description=f"You have declined the application for {self.user.mention}.",
                color=0x95A5A6  # Professional gray color
            )
            admin_embed.add_field(name="Application Type", value=self.application_name, inline=False)
            admin_embed.add_field(name="Feedback Provided", value=feedback, inline=False)
            if improvement:
                admin_embed.add_field(name="Improvement Suggestions", value=improvement, inline=False)
            if internal_notes:
                admin_embed.add_field(name="Internal Notes", value=internal_notes, inline=False)
            admin_embed.set_footer(text=f"Processed at {current_time.strftime('%Y-%m-%d %H:%M:%S')} UTC")

            try:
                await interaction.response.send_message(embed=admin_embed, ephemeral=True)
            except discord.errors.InteractionResponded:
                await interaction.followup.send(embed=admin_embed, ephemeral=True)

            # Send professional notification to applicant
            try:
                user_embed = discord.Embed(
                    title="Application Status Update",
                    description=f"Thank you for your interest in the **{self.application_name}** position. After careful review, we regret to inform you that your application has not been approved at this time.",
                    color=0x95A5A6  # Professional gray color
                )

                # Add feedback section
                user_embed.add_field(
                    name="Feedback from Our Team",
                    value=feedback,
                    inline=False
                )

                # Add improvement suggestions if provided
                if improvement:
                    user_embed.add_field(
                        name="Suggestions for Improvement",
                        value=improvement,
                        inline=False
                    )

                # Add encouragement section
                user_embed.add_field(
                    name="Next Steps",
                    value="• We encourage you to review the feedback provided\n• Consider applying again in the future\n• Feel free to reach out if you have any questions",
                    inline=False
                )

                # Add footer with timestamp
                user_embed.set_footer(text=f"© {datetime.now().year} Application System")
                user_embed.timestamp = current_time

                await self.user.send(embed=user_embed)
                logging.info(f"Decline notification sent to user {self.user.id} for {self.application_name} application")
            except discord.errors.Forbidden:
                logging.warning(f"Could not send DM to user {self.user.id} - DMs may be disabled")
            except Exception as dm_error:
                logging.error(f"Error sending DM to user {self.user.id}: {dm_error}")

            logging.info(f"Application for user {user_id_str} declined with feedback by {interaction.user.id}")

        except discord.errors.InteractionResponded:
            logging.warning("Interaction was already responded to in RejectReasonModal")
            try:
                await interaction.followup.send("Processing your response...", ephemeral=True)
            except:
                pass
        except Exception as e:
            logging.error(f"Error in RejectReasonModal.on_submit: {e}")
            try:
                await interaction.response.send_message("An error occurred while processing your response.", ephemeral=True)
            except discord.errors.InteractionResponded:
                try:
                    await interaction.followup.send("An error occurred while processing your response.", ephemeral=True)
                except:
                    pass
# Welcome Message System
@tree.command(name="set_welcome_channel", description="Set the welcome channel, message, and image/GIF")
@app_commands.default_permissions(administrator=True)
async def set_welcome_channel(interaction: discord.Interaction, channel: discord.TextChannel, message: str, image_url: str = None):
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        return
    global welcome_channel_id, welcome_message, welcome_image_url
    welcome_channel_id = channel.id
    welcome_message = message
    welcome_image_url = image_url
    await save_data()  # Save data after setting the welcome channel
    await interaction.response.send_message(f"Welcome channel set to {channel.mention} with message: {message}")

# This event handler has been consolidated with the main one defined later in the file





async def restore_ticket_panel():
    """Restore ticket panel with rate limit handling"""
    try:
        if not ticket_config["ticket_channel"]:
            return

        channel = bot.get_channel(ticket_config["ticket_channel"])
        if not channel:
            logging.warning("Ticket channel not found during panel restoration")
            return

        logging.info("Starting ticket panel restoration...")

        # Clear existing messages with rate limit handling
        async for message in channel.history(limit=100):
            if message.author == bot.bot:
                await rate_limiter.execute(
                    'delete_message',
                    message.delete
                )

        # Create new panel
        await create_ticket_panel(channel)
        logging.info("Ticket panel restored successfully")

    except Exception as e:
        log_error_to_console(e, "ticket panel restoration")

async def restore_reaction_roles():
    """Restore reaction roles panel on bot startup"""
    global reaction_message_id, reaction_channel_id
    try:
        logging.info("Starting reaction roles panel restoration...")

        if not reaction_channel_id or not reaction_message_id:
            logging.warning("No reaction role channel or message ID configured")
            return False

        channel = bot.get_channel(reaction_channel_id)
        if not channel:
            logging.error(f"Reaction role channel not found: {reaction_channel_id}")
            return False

        logging.info(f"Found reaction role channel: {channel.name}")

        # Try to get the existing message
        try:
            message = await channel.fetch_message(reaction_message_id)
            logging.info(f"Found existing reaction role message: {message.id}")

            # Clear existing reactions
            await message.clear_reactions()
            logging.info("Cleared existing reactions")
        except discord.NotFound:
            logging.warning(f"Reaction role message {reaction_message_id} not found, creating a new one")

            # Create a new message if the old one doesn't exist
            embed = discord.Embed(
                title="Reaction Roles",
                description="React to get roles!",
                color=discord.Color.blue()
            )
            message = await channel.send(embed=embed)

            # Update the message ID
            reaction_message_id = message.id

            # Save the updated message ID
            await save_data()
            logging.info(f"Created new reaction role message with ID: {message.id}")

        # Make sure reaction_roles has the correct structure
        if not isinstance(reaction_roles, dict) or "roles" not in reaction_roles:
            logging.error(f"Invalid reaction_roles structure: {reaction_roles}")
            reaction_roles["roles"] = {}
            reaction_roles["config"] = {"allow_multiple": True}

        # Update the embed with role information
        embed = discord.Embed(
            title="Reaction Roles",
            description="React with the emojis below to get or remove roles!",
            color=discord.Color.blue()
        )

        # Add fields for each role and add reactions
        for emoji_str, role_id in reaction_roles["roles"].items():
            try:
                # Convert role_id to int if it's a string
                if isinstance(role_id, str) and role_id.isdigit():
                    role_id = int(role_id)

                role = channel.guild.get_role(role_id)
                if role:
                    logging.info(f"Adding reaction role: {emoji_str} -> {role.name}")
                    embed.add_field(name=f"{emoji_str} {role.name}", value="React to get this role!", inline=False)
                    await message.add_reaction(emoji_str)
                else:
                    logging.warning(f"Role not found for ID: {role_id}")
            except Exception as e:
                logging.error(f"Error adding reaction for emoji {emoji_str}: {e}")

        # Update the message with the new embed
        await message.edit(embed=embed)
        logging.info("Reaction roles panel restored successfully")
        return True

    except Exception as e:
        logging.error(f"Error restoring reaction roles panel: {e}")
        import traceback
        traceback.print_exc()
        return False


@bot.event
async def on_message(message):
    try:
        # Ignore messages from the bot itself
        if message.author == bot.user:
            return

        # Process commands first
        await bot.process_commands(message)

        # Handle sticky messages with optimized performance
        if message.channel.id in sticky_messages:
            sticky_message = sticky_messages[message.channel.id]
            # Only check the last 3 messages for better performance in high-traffic channels
            async for msg in message.channel.history(limit=3):
                if msg.author == bot.user and msg.content == sticky_message:
                    return  # Sticky message is already present, no need to repost
            # Post sticky message if not found in recent messages
            await message.channel.send(sticky_message)
            print(f"Reposted sticky message in {message.channel.name}.")

        # Constants


# Handle Tebex webhook messages
        if message.webhook_id and message.channel.id == tebex_channel:
            try:
                content = message.content
                print(f"Received webhook content: {content}")

                if "has received a payment" in content:
                    parts = content.split("╽")
                    data = {}

                    # Parse the first part (store name)
                    data['store'] = parts[0].split("has received a payment")[0].strip()

                    # Parse remaining parts
                    for part in parts[1:]:
                        if ":" in part:
                            key, value = part.split(":", 1)
                            key = key.strip().lower()
                            value = value.strip()
                            data[key] = value

                    # Store transaction in MongoDB
                    transaction_data = {
                        'transaction_id': data.get('transaction id', '0'),
                        'buyer': data.get('from', 'N/A'),
                        'item': data.get('package', 'N/A'),
                        'price': data.get('price', 'N/A'),
                        'email': data.get('email', 'N/A'),
                        'timestamp': datetime.now(timezone.utc).isoformat(),
                        'chargeback': False
                    }

                    if save_transaction(transaction_data):
                        print(f"Transaction {transaction_data['transaction_id']} saved to MongoDB")
                    else:
                        print(f"Failed to save transaction {transaction_data['transaction_id']} to MongoDB")

                    # Create embed for notification
                    current_time = datetime.now(timezone.utc)
                    embed = discord.Embed(
                        title="Transaction Notification",
                        description="**Payment Successfully Processed**\n\nA new transaction has been completed and recorded in our secure payment system.",
                        color=0x2B2D31,
                        timestamp=current_time
                    )

                    # Add structured transaction details
                    embed.add_field(
                        name="📋 Transaction Details",
                        value=f"**Transaction ID:** `{data.get('transaction id', '0')}`\n**Amount:** `{data.get('price', '$0')}`\n**Product:** `{data.get('package', 'Unknown')}`",
                        inline=True
                    )

                    embed.add_field(
                        name="👤 Customer Information",
                        value=f"**Name:** `{data.get('from', 'Unknown')}`\n**Email:** `{data.get('email', 'No Email')}`",
                        inline=True
                    )

                    embed.add_field(
                        name="⏰ Processing Time",
                        value=f"**Completed:** {current_time.strftime('%B %d, %Y')}\n**Time:** {current_time.strftime('%I:%M %p UTC')}",
                        inline=False
                    )

                    # Set professional footer
                    embed.set_footer(
                        text=f"Secure Payment Processing System • {current_time.strftime('%m/%d/%Y %I:%M %p')}",
                        icon_url=message.guild.icon.url if message.guild.icon else None
                    )

                    # Try to delete the webhook message after sending the formatted one
                    try:
                        await delete_message_with_backoff(message)
                    except Exception as e:
                        print(f"Failed to delete webhook message: {e}")

                    # Send the formatted message
                    channel = bot.get_channel(tebex_channel)
                    if channel:
                        await channel.send(embed=embed)
                        print("Successfully sent purchase notification")
                    else:
                        print(f"Could not find channel with ID {tebex_channel}")

            except Exception as e:
                print(f"Error processing webhook message: {e}")
                traceback.print_exc()

        # Process commands after webhook handling
        await bot.process_commands(message)

    except Exception as e:
        print(f"Error in on_message event: {e}")
        traceback.print_exc()


# Sticky Message System
@tree.command(name="set_sticky_message", description="Set a sticky message in a channel")
@app_commands.default_permissions(administrator=True)
async def set_sticky_message(interaction: discord.Interaction, channel: discord.TextChannel, message: str):
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        return
    try:
        global sticky_messages
        sticky_messages[channel.id] = message

        # Delete any existing sticky messages
        async for msg in channel.history(limit=10):
            if msg.author == bot.user and msg.content == message:
                try:
                    await msg.delete()
                    await asyncio.sleep(0.5)  # Add a small delay between operations
                except Exception as e:
                    print(f"Error deleting existing sticky message: {e}")

        # Post the new sticky message immediately
        try:
            await channel.send(message)
            await interaction.response.send_message(f"Sticky message set in {channel.mention}: {message}")
        except Exception as e:
            print(f"Error setting sticky message: {e}")
            await interaction.response.send_message("Failed to set sticky message. Please try again.")
    except Exception as e:
        print(f"Error in set_sticky_message: {e}")
        await interaction.response.send_message("An error occurred while setting the sticky message.")

@bot.event
async def on_message(message):
    try:
        if message.author == bot.user:
            return

        await bot.process_commands(message)

        # Handle sticky messages
        if message.channel.id in sticky_messages:
            sticky_message = sticky_messages[message.channel.id]
            # First delete any existing sticky messages
            async for msg in message.channel.history(limit=10):
                if msg.author == bot.user and msg.content == sticky_message:
                    try:
                        await msg.delete()
                        await asyncio.sleep(0.5)  # Add a small delay between operations
                    except Exception as e:
                        print(f"Error deleting sticky message: {e}")
            # Then post the new sticky message
            try:
                await message.channel.send(sticky_message)
                print(f"Reposted sticky message in {message.channel.name}.")
            except Exception as e:
                print(f"Error posting sticky message: {e}")

        # Handle Tebex webhook messages
        if message.webhook_id and message.channel.id == tebex_channel:
            try:
                content = message.content
                print(f"Received webhook content: {content}")

                if "has received a payment" in content:
                    parts = content.split("╽")
                    data = {}

                    # Parse the first part (store name)
                    data['store'] = parts[0].split("has received a payment")[0].strip()

                    # Parse remaining parts
                    for part in parts[1:]:
                        if ":" in part:
                            key, value = part.split(":", 1)
                            key = key.strip().lower()
                            value = value.strip()
                            data[key] = value

                    # Get current time
                    current_time = datetime.now(timezone.utc)

                        # Store transaction in MongoDB
                    transaction_data = {
                        'buyer': data.get('from', 'Unknown'),
                        'item': data.get('package', 'Unknown'),
                        'price': data.get('price', '$0'),
                        'email': data.get('email', 'No Email'),
                        'timestamp': current_time,
                        'transaction_id': data.get('transaction id', '0'),
                        'chargeback': False
                    }

                    # Save transaction to MongoDB using the imported function
                    save_transaction(transaction_data)

                    # Create and send embed
                    embed = discord.Embed(
                        title="Transaction Notification",
                        description="**Payment Successfully Processed**\n\nA new transaction has been completed and recorded in our secure payment system.",
                        color=0x2B2D31,
                        timestamp=current_time
                    )

                    # Add structured transaction details
                    embed.add_field(
                        name="📋 Transaction Details",
                        value=f"**Transaction ID:** `{data.get('transaction id', '0')}`\n**Amount:** `{data.get('price', '$0')}`\n**Product:** `{data.get('package', 'Unknown')}`",
                        inline=True
                    )

                    embed.add_field(
                        name="👤 Customer Information",
                        value=f"**Name:** `{data.get('from', 'Unknown')}`\n**Email:** `{data.get('email', 'No Email')}`",
                        inline=True
                    )

                    embed.add_field(
                        name="⏰ Processing Time",
                        value=f"**Completed:** {current_time.strftime('%B %d, %Y')}\n**Time:** {current_time.strftime('%I:%M %p UTC')}",
                        inline=False
                    )

                    # Set professional footer
                    embed.set_footer(
                        text=f"Secure Payment Processing System • {current_time.strftime('%m/%d/%Y %I:%M %p')}",
                        icon_url=message.guild.icon.url if message.guild.icon else None
                    )

                    # Use rate-limited delete
                    await delete_message_with_backoff(message)

                    # Send the formatted message
                    channel = bot.get_channel(tebex_channel)
                    if channel:
                        await channel.send(embed=embed)
                        print("Successfully sent purchase notification")
                    else:
                        print(f"Could not find channel with ID {tebex_channel}")

            except Exception as e:
                print(f"Error processing webhook message: {e}")
                traceback.print_exc()

    except Exception as e:
        print(f"Error in on_message event: {e}")
        traceback.print_exc()


@tree.command(name="remove_sticky_message", description="Remove the sticky message from a channel")
@app_commands.default_permissions(administrator=True)
async def remove_sticky_message(interaction: discord.Interaction, channel: discord.TextChannel):
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        return
    print("Remove sticky message command invoked")  # Debugging output
    global sticky_messages
    if channel.id in sticky_messages:
        del sticky_messages[channel.id]
        await interaction.response.send_message(f"Sticky message removed from {channel.mention}.")
    else:
        await interaction.response.send_message(f"No sticky message found in {channel.mention}.")

# Load or initialize JSON files
def load_json(file_path):
    try:
        if not os.path.exists(file_path):
            # Create file with empty dictionary if it doesn't exist
            with open(file_path, 'w') as f:
                json.dump({}, f)
            return {}

        with open(file_path, 'r') as f:
            content = f.read()
            if not content:  # If file is empty
                return {}
            return json.loads(content)
    except Exception as e:
        print(f"Error loading JSON file: {e}")
        return {}

def save_json(file_path, data):
    try:
        with open(file_path, 'w') as f:
            json.dump(data, f, indent=4)
        return True
    except Exception as e:
        print(f"Error saving JSON file: {e}")
        return False


# Log purchase events from webhook




# Reaction Role System
@tree.command(name="setup_reaction_roles", description="Set up reaction roles with a custom message")
@app_commands.default_permissions(administrator=True)
async def setup_reaction_roles(
    interaction: discord.Interaction,
    channel: discord.TextChannel,
    message: str,
    allow_multiple: bool = True
):
    if not log_permission_check(interaction, "setup_reaction_roles"):
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        return
    """Set up reaction roles in a channel with a custom message"""
    global reaction_message_id, reaction_channel_id, reaction_roles

    try:
        # Create and send the initial embed
        embed = discord.Embed(
            title="Reaction Roles",
            description=message,
            color=discord.Color.blue()
        )
        msg = await channel.send(embed=embed)

        # Update global variables
        reaction_message_id = msg.id
        reaction_channel_id = channel.id

        logging.info(f"Created new reaction roles message with ID: {msg.id} in channel: {channel.id}")

        # Create a clean reaction_roles structure
        reaction_roles = {
            "config": {
                "allow_multiple": allow_multiple
            },
            "roles": {}
        }

        # Save the reaction roles data
        await save_reaction_roles_data()

        # Send instructions
        await interaction.response.send_message(
            "Now, let's add roles. Send messages in this format:\n"
            "`emoji @role`\n"
            "Example: 🎮 @Gamer\n"
            "Type 'done' when finished."
        )

        def message_check(m):
            return m.author == interaction.user and m.channel == interaction.channel and (m.content.lower() == 'done' or (len(m.content.split()) == 2 and len(m.role_mentions) > 0))

        # Wait for role additions
        while True:
            try:
                response = await bot.wait_for('message', timeout=300.0, check=message_check)

                if response.content.lower() == 'done':
                    # Final save before completing
                    await save_reaction_roles_data()
                    await interaction.followup.send("Reaction role setup complete!")
                    break

                # Parse emoji and role from response
                parts = response.content.split()
                if len(parts) != 2:
                    await interaction.followup.send("Invalid format. Please use: `emoji @role`")
                    continue

                emoji = parts[0]
                role_id = int(parts[1].strip('<@&>'))
                role = interaction.guild.get_role(role_id)

                if not role:
                    await interaction.followup.send("Invalid role. Please try again.")
                    continue

                # Update embed with new role
                embed.add_field(name=f"{emoji} {role.name}", value="React to get this role!", inline=False)
                await msg.edit(embed=embed)

                # Add to reaction_roles and add reaction
                reaction_roles["roles"][emoji] = role.id

                logging.info(f"Adding reaction role: emoji={emoji}, role={role.name} (ID: {role.id})")

                # Save after each role is added
                await save_reaction_roles_data()

                await msg.add_reaction(emoji)
                await response.add_reaction('✅')

            except asyncio.TimeoutError:
                # Final save before timing out
                await save_reaction_roles_data()
                await interaction.followup.send("Setup timed out. The message has been created with any roles that were added.")
                break
            except Exception as e:
                await interaction.followup.send(f"Error adding role: {str(e)}")
                continue

    except discord.Forbidden:
        await interaction.response.send_message("I don't have the required permissions to set up reaction roles.", ephemeral=True)
    except Exception as e:
        await interaction.response.send_message(f"An error occurred: {str(e)}", ephemeral=True)


async def save_reaction_roles_data():
    """Helper function to save reaction roles data"""
    try:
        logging.info("Saving reaction roles data...")

        # Create a data structure to save
        reaction_data = {
            "message_id": reaction_message_id,
            "channel_id": reaction_channel_id,
            "roles": reaction_roles["roles"],
            "config": reaction_roles["config"]
        }

        # Connect to MongoDB directly
        try:
            client = pymongo.MongoClient("mongodb://localhost:27017/")
            db = client["missminutesbot"]
            collection = db["reaction_roles"]

            # Update the document
            result = collection.replace_one(
                {"_id": "reaction_roles"},
                {"_id": "reaction_roles", **reaction_data},
                upsert=True
            )

            if result.acknowledged:
                logging.info("Reaction roles data saved successfully to MongoDB")
                return True
            else:
                logging.error("MongoDB did not acknowledge the save operation")
                return False

        except Exception as mongo_error:
            logging.error(f"MongoDB error: {mongo_error}")

            # Fallback to the regular save method
            logging.info("Falling back to regular save method...")

            # Load current data
            data = await load_data() or {}

            # Update the reaction_roles data
            data["reaction_roles"] = reaction_data

            # Save the data
            success = await save_data()
            if success:
                logging.info("Reaction roles data saved successfully using fallback method")
            else:
                logging.error("Failed to save reaction roles data using fallback method")

            return success

    except Exception as e:
        logging.error(f"Error saving reaction roles data: {e}")
        import traceback
        traceback.print_exc()
        return False


@bot.event
async def on_raw_reaction_add(payload):
    # Ignore bot reactions
    if payload.user_id == bot.user.id:
        return

    # Check if this is a reaction role message
    if payload.message_id == reaction_message_id:
        emoji = str(payload.emoji)
        # Check if reaction_roles has the expected structure
        if "roles" in reaction_roles and emoji in reaction_roles["roles"]:
            guild = bot.get_guild(payload.guild_id)
            if not guild:
                return

            role = guild.get_role(reaction_roles["roles"][emoji])
            if not role:
                return

            member = guild.get_member(payload.user_id)
            if not member:
                return

            try:
                # If multiple roles are not allowed, remove other reaction roles first
                if "config" in reaction_roles and not reaction_roles["config"]["allow_multiple"]:
                    for role_id in reaction_roles["roles"].values():
                        if role_id != reaction_roles["roles"][emoji]:
                            old_role = guild.get_role(role_id)
                            if old_role and old_role in member.roles:
                                await member.remove_roles(old_role)

                await member.add_roles(role)
            except discord.HTTPException:
                pass

@bot.event
async def on_raw_reaction_remove(payload):
    # Check if this is a reaction role message
    if payload.message_id == reaction_message_id:
        emoji = str(payload.emoji)
        # Check if reaction_roles has the expected structure
        if "roles" in reaction_roles and emoji in reaction_roles["roles"]:
            guild = bot.get_guild(payload.guild_id)
            if not guild:
                return

            role = guild.get_role(reaction_roles["roles"][emoji])
            if not role:
                return

            member = guild.get_member(payload.user_id)
            if not member:
                return

            try:
                await member.remove_roles(role)
            except discord.HTTPException:
                pass

# Tebex-related commands
@tree.command(name="validate_purchase", description="Validate a purchase by transaction ID")
@app_commands.default_permissions(administrator=True)
async def validate_purchase(interaction: discord.Interaction, transaction_id: str):
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        return

    try:
        # Get transactions from database
        transactions = await get_transactions(limit=100, skip=0, sort_by="timestamp", sort_dir=-1)
        purchase_details = None

        # Find the transaction with matching ID
        for transaction in transactions:
            if transaction.get('transaction_id') == transaction_id:
                purchase_details = transaction
                break

        if purchase_details:
            # Determine embed color based on chargeback status
            is_chargeback = purchase_details.get('chargeback', False)
            color = discord.Color.red() if is_chargeback else discord.Color.green()

            # Convert timestamp to datetime if it's a string
            timestamp = purchase_details['timestamp']
            if isinstance(timestamp, str):
                timestamp = datetime.fromisoformat(timestamp)

            # Create professional title and description
            if is_chargeback:
                title = "Transaction Verification ⚠️ CHARGEBACK"
                description = "**Security Alert - Chargeback Detected**\n\nThis transaction has been flagged as a chargeback in our fraud prevention system."
            else:
                title = "Transaction Verification"
                description = "**Transaction Validation Complete**\n\nThe requested transaction has been successfully verified in our secure payment database."

            embed = discord.Embed(
                title=title,
                description=description,
                color=color,
                timestamp=timestamp
            )

            # Add structured transaction details
            embed.add_field(
                name="📋 Transaction Details",
                value=f"**Transaction ID:** `{transaction_id}`\n**Amount:** `{purchase_details.get('price', 'N/A')}`\n**Product:** `{purchase_details.get('item', 'N/A')}`",
                inline=True
            )

            embed.add_field(
                name="👤 Customer Information",
                value=f"**Name:** `{purchase_details.get('buyer', 'N/A')}`\n**Email:** `{purchase_details.get('email', 'Not provided')}`",
                inline=True
            )

            embed.add_field(
                name="🔒 Security Status",
                value=f"**Status:** {'🚫 Chargeback Flagged' if is_chargeback else '✅ Verified Transaction'}\n**Risk Level:** {'High' if is_chargeback else 'Low'}",
                inline=False
            )

            # Set professional footer
            embed.set_footer(
                text=f"Transaction Verification System • {timestamp.strftime('%m/%d/%Y %I:%M %p')}"
            )

            await interaction.response.send_message(embed=embed)
        else:
            await interaction.response.send_message("Purchase not found.", ephemeral=True)
    except Exception as e:
        logging.error(f"Error in validate_purchase: {e}")
        await interaction.response.send_message("An error occurred while validating the purchase.", ephemeral=True)

@tree.command(name="lookup_transaction", description="Lookup a transaction by ID")
@app_commands.default_permissions(administrator=True)
async def lookup_transaction(interaction: discord.Interaction, transaction_id: str):
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        return
    try:
        # Query transaction from MongoDB using our async function
        transactions = await get_transactions(limit=100, skip=0, sort_by="timestamp", sort_dir=-1)
        transaction_details = None

        # Find the transaction with matching ID
        for transaction in transactions:
            if transaction.get('transaction_id') == transaction_id:
                transaction_details = transaction
                break

        if not transaction_details:
            await interaction.response.send_message("Transaction not found.", ephemeral=True)
            return

        # Determine embed color based on chargeback status
        color = discord.Color.red() if transaction_details.get('chargeback', False) else discord.Color.blue()

        # Convert timestamp string to datetime object if it's a string
        timestamp = transaction_details['timestamp']
        if isinstance(timestamp, str):
            timestamp = datetime.fromisoformat(timestamp)

        # Create professional title and description
        is_chargeback = transaction_details.get('chargeback', False)
        if is_chargeback:
            title = "Transaction Lookup ⚠️ CHARGEBACK"
            description = "**Security Alert - Chargeback Detected**\n\nThis transaction has been flagged as a chargeback in our fraud prevention system."
        else:
            title = "Transaction Lookup"
            description = "**Transaction Record Retrieved**\n\nComplete transaction details have been successfully retrieved from our secure payment database."

        embed = discord.Embed(
            title=title,
            description=description,
            color=color,
            timestamp=timestamp
        )

        # Add structured transaction details
        embed.add_field(
            name="📋 Transaction Details",
            value=f"**Transaction ID:** `{transaction_id}`\n**Amount:** `{transaction_details.get('price', 'N/A')}`\n**Product:** `{transaction_details.get('item', 'N/A')}`",
            inline=True
        )

        embed.add_field(
            name="👤 Customer Information",
            value=f"**Name:** `{transaction_details.get('buyer', 'N/A')}`\n**Email:** `{transaction_details.get('email', 'Not provided') if 'email' in transaction_details else 'Not provided'}`",
            inline=True
        )

        embed.add_field(
            name="🔒 Security Status",
            value=f"**Status:** {'🚫 Chargeback Flagged' if is_chargeback else '✅ Verified Transaction'}\n**Risk Level:** {'High' if is_chargeback else 'Low'}",
            inline=False
        )

        # Set professional footer
        embed.set_footer(
            text=f"Transaction Lookup System • {timestamp.strftime('%m/%d/%Y %I:%M %p')}"
        )

        await interaction.response.send_message(embed=embed)
    except Exception as e:
        logging.error(f"Error in lookup_transaction: {e}")
        await interaction.response.send_message("An error occurred while looking up the transaction.", ephemeral=True)

@tree.command(name="markchargeback", description="Mark a transaction as a chargeback")
@app_commands.default_permissions(administrator=True)
async def markchargeback(interaction: discord.Interaction, transaction_id: str):
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        return

    try:
        # First, get the transaction
        transactions = await get_transactions(limit=100, skip=0, sort_by="timestamp", sort_dir=-1)
        transaction = None

        # Find the transaction with matching ID
        for t in transactions:
            if t.get('transaction_id') == transaction_id:
                transaction = t
                break

        if not transaction:
            await interaction.response.send_message("Transaction not found.", ephemeral=True)
            return

        # Update the transaction with chargeback status
        transaction['chargeback'] = True

        # Save the updated transaction
        success = await save_transaction(transaction)

        if not success:
            await interaction.response.send_message("Failed to update transaction. Please try again.", ephemeral=True)
            return

        # Create response embed
        current_time = datetime.now(timezone.utc)
        embed = discord.Embed(
            title="Chargeback Status Updated",
            description="**Security Action Completed**\n\nThe transaction has been successfully flagged as a chargeback in our fraud prevention system.",
            color=0xE74C3C,  # Professional red
            timestamp=current_time
        )

        # Add structured transaction details
        embed.add_field(
            name="📋 Transaction Details",
            value=f"**Transaction ID:** `{transaction_id}`\n**Amount:** `{transaction.get('price', 'N/A')}`\n**Product:** `{transaction.get('item', 'N/A')}`",
            inline=True
        )

        embed.add_field(
            name="👤 Customer Information",
            value=f"**Name:** `{transaction.get('buyer', 'N/A')}`\n**Email:** `{transaction.get('email', 'Not provided') if 'email' in transaction else 'Not provided'}`",
            inline=True
        )

        embed.add_field(
            name="⚠️ Security Action",
            value=f"**Status:** 🚫 Chargeback Flagged\n**Updated:** {current_time.strftime('%B %d, %Y at %I:%M %p UTC')}",
            inline=False
        )

        # Set professional footer
        embed.set_footer(
            text=f"Fraud Prevention System • {current_time.strftime('%m/%d/%Y %I:%M %p')}"
        )

        await interaction.response.send_message(embed=embed)

    except Exception as e:
        logging.error(f"Error in markchargeback command: {e}")
        await interaction.response.send_message(
            "An error occurred while processing the command. Please try again later.",
            ephemeral=True
        )

# Set Tebex webhook channel
@tree.command(name="set_tebex_channel", description="Set up Tebex notification channels")
@app_commands.default_permissions(administrator=True)
async def set_tebex_channel(
    interaction: discord.Interaction,
    tebex_webhook: str,
    output_channel: discord.TextChannel
):
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        return
    """
    Set up Tebex channels:
    tebex_webhook: Discord webhook URL where Tebex will send notifications
    output_channel: Channel where formatted purchase messages will be sent
    """
    global tebex_channel, webhook_url

    try:
        # Validate webhook URL format
        if not tebex_webhook.startswith('https://discord.com/api/webhooks/'):
            await interaction.response.send_message(
                "Invalid webhook URL. Please provide a valid Discord webhook URL.",
                ephemeral=True
            )
            return

        # Store the webhook URL and channel ID
        webhook_url = tebex_webhook
        tebex_channel = output_channel.id

        # Create embed with setup information
        current_time = datetime.now(timezone.utc)
        embed = discord.Embed(
            title="Payment System Configuration",
            description="**Integration Setup Complete**\n\nYour Tebex payment system has been successfully configured and is ready for secure transaction processing.",
            color=0x2ECC71,  # Professional green
            timestamp=current_time
        )

        # Add structured configuration details
        embed.add_field(
            name="🔗 Integration Status",
            value=f"**Webhook:** ✅ Configured\n**Channel:** {output_channel.mention}\n**Status:** Active",
            inline=True
        )

        embed.add_field(
            name="🛡️ Security Features",
            value="**Encryption:** Enabled\n**Validation:** Active\n**Monitoring:** Real-time",
            inline=True
        )

        embed.add_field(
            name="📋 Next Steps",
            value=f"**Complete Setup:** Follow instructions below\n**Test Integration:** Send test transaction\n**Monitor:** Check {output_channel.mention}",
            inline=False
        )

        # Add Tebex setup instructions with professional formatting
        embed.add_field(
            name="🔧 Tebex Dashboard Configuration",
            value=(
                "**Step 1:** Access your Tebex merchant dashboard\n"
                "**Step 2:** Navigate to **Webhooks** → **Settings**\n"
                "**Step 3:** Create new webhook endpoint\n"
                "**Step 4:** Configure notification format (see below)\n"
                "**Step 5:** Test integration and verify functionality"
            ),
            inline=False
        )

        # Add notification structure with enhanced formatting
        embed.add_field(
            name="📝 Required Webhook Format",
            value=(
                "```yaml\n"
                "# Exact format required - do not modify\n"
                "{webstore} has received a payment ╽ "
                "From: {username} ╽ "
                "Price: {price} ╽ "
                "Package: {packagename} ╽ "
                "Transaction ID: {transactionid} ╽ "
                "Email: {email}\n"
                "```\n"
                "⚠️ **Critical:** Structure must match exactly, including `╽` separators"
            ),
            inline=False
        )

        # Set professional footer
        embed.set_footer(
            text=f"Payment Integration System • {current_time.strftime('%m/%d/%Y %I:%M %p')}"
        )

        # Save the settings
        await save_data()

        # Send the confirmation embed
        await interaction.response.send_message(embed=embed, ephemeral=True)

    except Exception as e:
        await interaction.response.send_message(
            f"An error occurred: {str(e)}",
            ephemeral=True
        )

# Add purchase with improved formatting and validation
@tree.command(name="add_purchase", description="Add a purchase to the database")
@app_commands.default_permissions(administrator=True)
async def add_purchase(interaction: discord.Interaction, transaction_id: str, buyer: str, item: str, price: str, email: str = None):
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        return
    if not tebex_channel:
        await interaction.response.send_message("Tebex system is not configured. Please use /set_tebex_channel first.", ephemeral=True)
        return

    # Create transaction document
    transaction = {
        'transaction_id': transaction_id,
        'buyer': buyer,
        'item': item,
        'price': price,
        'email': email,
        'timestamp': datetime.now(timezone.utc).isoformat(),
        'chargeback': False
    }

    try:
        # Use our async save_transaction function instead of direct collection access
        success = await save_transaction(transaction)

        if not success:
            await interaction.response.send_message("Failed to save transaction. Please try again.", ephemeral=True)
            return

        current_time = datetime.now(timezone.utc)
        embed = discord.Embed(
            title="Transaction Successfully Recorded",
            description="**Manual Transaction Entry Completed**\n\nThe transaction has been successfully added to our secure payment database.",
            color=0x2ECC71,  # Professional green
            timestamp=current_time
        )

        # Add structured transaction details
        embed.add_field(
            name="📋 Transaction Details",
            value=f"**Transaction ID:** `{transaction_id}`\n**Amount:** `{price}`\n**Product:** `{item}`",
            inline=True
        )

        embed.add_field(
            name="👤 Customer Information",
            value=f"**Name:** `{buyer}`\n**Email:** `{email if email else 'Not provided'}`",
            inline=True
        )

        embed.add_field(
            name="⏰ Processing Time",
            value=f"**Added:** {current_time.strftime('%B %d, %Y')}\n**Time:** {current_time.strftime('%I:%M %p UTC')}",
            inline=False
        )

        # Set professional footer
        embed.set_footer(
            text=f"Manual Entry System • {current_time.strftime('%m/%d/%Y %I:%M %p')}"
        )

        await interaction.response.send_message(embed=embed)
    except Exception as e:
        logging.error(f"Error adding purchase: {e}")
        await interaction.response.send_message("An error occurred while adding the purchase.", ephemeral=True)

# Function to save data

async def save_data():
    global gang_strikes, gang_roles, gang_members, gang_leaders, gang_invitations
    global application_forms, application_channel, application_log_channel, sticky_messages
    global welcome_channel_id, welcome_message, welcome_image_url, vanity_url, role_name, notification_channel_id
    global join_role_id, reaction_message_id, reaction_channel_id, emoji_role_map, tebex_channel, webhook_url
    global applications_status

    try:
        # Convert numeric keys to strings in gang data
        processed_gang_roles = {}
        for gang_name, gang_data in gang_roles.items():
            processed_gang_data = gang_data.copy()
            processed_gang_data['leader'] = str(gang_data['leader'])
            processed_gang_data['leader_role'] = str(gang_data['leader_role'])
            processed_gang_data['members'] = [str(member_id) for member_id in gang_data['members']]
            processed_gang_roles[gang_name] = processed_gang_data

        processed_gang_leaders = {str(k): str(v) for k, v in gang_leaders.items()}
        processed_gang_members = {str(k): v for k, v in gang_members.items()}
        processed_gang_strikes = {str(k): v for k, v in gang_strikes.items()}

        data = {
            "gangs": {
                "roles": processed_gang_roles,
                "members": processed_gang_members,
                "leaders": processed_gang_leaders,
                "strikes": processed_gang_strikes,
                "invitations": gang_invitations  # Save pending gang invitations
            },
            "applications": {
                "forms": application_forms,
                "channels": {
                    "application_channel": application_channel,
                    "log_channel": application_log_channel
                },
                "status": applications_status
            },
            "settings": {
                "welcome": {
                    "channel_id": welcome_channel_id,
                    "message": welcome_message,
                    "image_url": welcome_image_url
                },
                "vanity": {
                    "url": vanity_url,
                    "role_name": role_name
                },
                "notifications": {
                    "channel_id": notification_channel_id
                },
                "join_role_id": join_role_id
            },
            "sticky_messages": sticky_messages,
            "tebex_settings": {
                "channel_id": tebex_channel,
                "webhook_url": webhook_url
            },
            "reaction_roles": {
                "roles": reaction_roles,
                "message_id": reaction_message_id,
                "channel_id": reaction_channel_id
            }
        }

        # Save data to MongoDB
        from database import save_data as db_save_data
        success = await db_save_data(data)

        if success:
            logging.info("Data saved successfully to MongoDB")
        else:
            logging.error("Failed to save data to MongoDB")

    except Exception as e:
        logging.error(f"Error saving data: {e}")


# Function to load data on startup
async def load_data():
    global gang_strikes, gang_roles, gang_members, gang_leaders, gang_invitations
    global application_forms, application_channel, application_log_channel, sticky_messages
    global welcome_channel_id, welcome_message, welcome_image_url, vanity_url, role_name, notification_channel_id
    global join_role_id, reaction_message_id, reaction_channel_id, emoji_role_map, tebex_channel, webhook_url
    global applications_status

    logging.info("Loading bot data from MongoDB...")
    try:
        # Connect to MongoDB
        client = pymongo.MongoClient("mongodb://localhost:27017/")
        db = client["missminutesbot"]

        # Load data from MongoDB collections
        data = {}

        # Load gangs data
        gangs_data = db["gangs"].find_one({"_id": "gangs"})
        if gangs_data:
            data["gangs"] = {k: v for k, v in gangs_data.items() if k != "_id"}
            logging.debug(f"Raw gangs_data from database: {gangs_data}")  # Debug: Show raw database data

        # Load applications data
        applications_data = db["applications"].find_one({"_id": "applications"})
        if applications_data:
            data["applications"] = {k: v for k, v in applications_data.items() if k != "_id"}

        # Load settings data
        settings_data = db["settings"].find_one({"_id": "settings"})
        if settings_data:
            data["settings"] = {k: v for k, v in settings_data.items() if k != "_id"}

        # Load sticky messages
        sticky_data = db["sticky_messages"].find_one({"_id": "sticky_messages"})
        if sticky_data:
            data["sticky_messages"] = sticky_data.get("messages", {})

        # Load tebex settings
        tebex_data = db["tebex_settings"].find_one({"_id": "tebex_settings"})
        if tebex_data:
            data["tebex_settings"] = {k: v for k, v in tebex_data.items() if k != "_id"}

        # Load reaction roles
        reaction_data = db["reaction_roles"].find_one({"_id": "reaction_roles"})
        if reaction_data:
            data["reaction_roles"] = {k: v for k, v in reaction_data.items() if k != "_id"}

        logging.debug("Data loaded from MongoDB.")

        # Load gang data with proper type conversion
        gangs_data = data.get("gangs", {})
        gang_roles_data = gangs_data.get("roles", {})

        # Convert gang_roles data with new permission system
        gang_roles = {}
        for gang_name, gang_data in gang_roles_data.items():
            gang_roles[gang_name] = {
                "leader": int(gang_data["leader"]),
                "leader_role": int(gang_data["leader_role"]),
                "members": [int(m) if isinstance(m, str) and m.isdigit() else m for m in gang_data["members"]],
                "member_limit": gang_data.get("member_limit", 5),
                "current_members": gang_data.get("current_members", 0),
                "member_management_permissions": gang_data.get("member_management_permissions", "leader_only"),  # Default to leader_only for existing gangs
                "officers": gang_data.get("officers", [])  # Default to empty list for existing gangs
            }

        # Convert gang_leaders data
        gang_leaders_data = gangs_data.get("leaders", {})
        gang_leaders = {int(k): int(v) for k, v in gang_leaders_data.items() if k.isdigit() and str(v).isdigit()}

        gang_members = gangs_data.get("members", {})
        gang_strikes = gangs_data.get("strikes", {})
        gang_invitations = gangs_data.get("invitations", {})  # Load pending gang invitations

        logging.info(f"Loaded gang data: {len(gang_roles)} gangs, {len(gang_leaders)} leaders, {len(gang_invitations)} invitations")

        # Log invitation details to file only
        if gang_invitations:
            logging.debug("Gang invitations found:")
            for inv_id, inv_data in gang_invitations.items():
                logging.debug(f"  - {inv_id}: {inv_data.get('gang_name', 'Unknown')} (Target: {inv_data.get('target_id', 'Unknown')})")
        else:
            logging.debug("No gang invitations found in database")

        # Load applications
        applications = data.get("applications", {})
        application_forms = applications.get("forms", {})
        application_channel = applications.get("channels", {}).get("application_channel")
        application_log_channel = applications.get("channels", {}).get("log_channel")
        applications_status = applications.get("status", {})

        logging.info(f"Loaded applications: {len(application_forms)} forms, {len(applications_status)} status entries")

        # Load settings
        settings = data.get("settings", {})
        welcome = settings.get("welcome", {})
        welcome_channel_id = welcome.get("channel_id")
        welcome_message = welcome.get("message")
        welcome_image_url = welcome.get("image_url")

        logging.debug(f"Loaded welcome settings: ChannelID={welcome_channel_id}")

        vanity = settings.get("vanity", {})
        vanity_url = vanity.get("url")
        role_name = vanity.get("role_name")

        logging.debug(f"Loaded vanity settings: URL={vanity_url}, RoleName={role_name}")

        notifications = settings.get("notifications", {})
        notification_channel_id = notifications.get("channel_id")

        logging.debug(f"Loaded notification settings: ChannelID={notification_channel_id}")

        join_role_id = settings.get("join_role_id")

        # Load sticky messages
        sticky_messages = data.get("sticky_messages", {})

        logging.info(f"Loaded {len(sticky_messages)} sticky messages")

        # Load Tebex settings
        tebex_settings = data.get("tebex_settings", {})
        tebex_channel = tebex_settings.get("channel_id")
        webhook_url = tebex_settings.get("webhook_url")

        logging.debug(f"Loaded Tebex settings: ChannelID={tebex_channel}")

        # Load reaction roles
        reaction_data = data.get("reaction_roles", {})
        global reaction_roles

        # Ensure we have a clean, consistent structure
        roles_dict = {}

        # Extract roles from the data
        if "roles" in reaction_data and isinstance(reaction_data["roles"], dict):
            # Check if it's a flat structure (emoji -> role_id)
            has_flat_structure = all(
                isinstance(k, str) and isinstance(v, (int, str)) and k not in ('config', 'roles')
                for k, v in reaction_data["roles"].items()
            )

            if has_flat_structure:
                roles_dict = reaction_data["roles"]
            # If it's nested, try to extract the emoji->role_id mapping
            elif "roles" in reaction_data["roles"] and isinstance(reaction_data["roles"]["roles"], dict):
                roles_dict = reaction_data["roles"]["roles"]

        # Get config
        config_dict = {"allow_multiple": False}  # Default config
        if "config" in reaction_data:
            config_dict = reaction_data["config"]
        elif "roles" in reaction_data and "config" in reaction_data["roles"]:
            config_dict = reaction_data["roles"]["config"]

        # Set the clean structure
        reaction_roles = {
            "roles": roles_dict,
            "config": config_dict
        }

        global reaction_message_id, reaction_channel_id
        reaction_message_id = reaction_data.get("message_id")
        reaction_channel_id = reaction_data.get("channel_id")

        logging.info(f"Loaded reaction roles: {len(roles_dict)} roles, MessageID={reaction_message_id}, ChannelID={reaction_channel_id}")

        logging.info("Bot data loaded successfully from MongoDB")

    except json.JSONDecodeError as e:
        log_error_to_console(e, "JSON decoding")
    except Exception as e:
        log_error_to_console(e, "data loading")

# Event Handlers Section
@bot.event
async def on_ready():
    """
    Bot initialization with optimized startup sequence and error handling
    - Loads configuration data
    - Syncs commands with retry logic
    - Starts background tasks
    - Initializes memory management
    - Restores interactive components (buttons, reaction roles)
    """
    log_bot_status(f'Logged in as {bot.user} (ID: {bot.user.id})', "SUCCESS")
    logging.info(f'Bot logged in as {bot.user} (ID: {bot.user.id})')

    # Initialize performance optimization systems
    if PERFORMANCE_OPTIMIZATIONS_ENABLED:
        try:
            logging.info("Initializing performance optimization systems...")

            # Start performance manager
            performance_manager = get_performance_manager()
            await performance_manager.start()

            # Start enhanced database manager
            enhanced_db = get_enhanced_db_manager()
            await enhanced_db.start()

            # Ensure database connection is established
            if not await enhanced_db.ensure_connection():
                logging.warning("Enhanced database connection failed, will use fallback")
            else:
                logging.info("Enhanced database connected successfully")

            # Start Discord API optimizer
            api_optimizer = get_discord_api_optimizer()
            await api_optimizer.start()

            # Register health checks
            performance_manager.register_health_check(
                "database_connection",
                lambda: enhanced_db.connection_pool.is_connected
            )
            performance_manager.register_health_check(
                "memory_usage",
                lambda: get_memory_manager().get_memory_usage() < 2048  # 2GB threshold
            )

            log_bot_status("Performance optimization systems initialized", "SUCCESS")

        except Exception as e:
            log_bot_status(f"Performance optimization initialization failed: {e}", "WARNING")
            logging.warning(f"Performance optimization initialization failed: {e}")

    try:
        # Start memory manager for optimized resource usage
        memory_manager = get_memory_manager()
        memory_manager.start()
        logging.info("Memory manager started")

        # Load ticket data first
        logging.info("Loading ticket configuration...")
        success = await load_ticket_data()
        if not success:
            logging.warning("Failed to load ticket configuration")

        # Load rest of data with optimization
        logging.info("Loading bot data...")
        if PERFORMANCE_OPTIMIZATIONS_ENABLED:
            await optimized_load_data()
            logging.info("Bot data loaded successfully (optimized)")
        else:
            await load_data()
            logging.info("Bot data loaded successfully")

        # Implement retry logic for command sync
        max_retries = 3
        retry_delay = 5  # seconds

        for attempt in range(max_retries):
            try:
                logging.info(f"Starting command sync process (attempt {attempt + 1}/{max_retries})...")
                synced = await tree.sync()
                logging.info(f"Synced {len(synced)} command(s)!")
                break
            except discord.errors.DiscordServerError as e:
                if attempt < max_retries - 1:
                    logging.warning(f"Discord service temporarily unavailable. Retrying in {retry_delay} seconds...")
                    await asyncio.sleep(retry_delay)
                    retry_delay *= 2  # Exponential backoff
                else:
                    logging.error(f"Failed to sync commands after all retries: {e}")

        # Start background tasks
        check_vanity_status.start()
        logging.info("Started vanity status check task")

        # Register caches with memory manager for automatic cleanup
        memory_manager.register_cache("sticky_messages", max_size=100, default_ttl=3600)
        memory_manager.register_cache("guild_settings", max_size=500, default_ttl=1800)
        memory_manager.register_cache("transactions", max_size=1000, default_ttl=3600)

        # Restore application buttons
        await restore_application_buttons()
        logging.info("Restored application buttons")

        # Restore reaction roles panel
        await restore_reaction_roles()
        logging.info("Restored reaction roles panel")

        # Clean up expired gang invitations first
        await cleanup_expired_gang_invitations()
        logging.info("Cleaned up expired gang invitations")

        # Restore gang invitation views for persistent buttons
        logging.debug(f"About to restore gang invitation views. Current gang_invitations count: {len(gang_invitations)}")
        await restore_gang_invitation_views()
        logging.info("Restored gang invitation views")

        # Recreate application dropdown menu if channel exists
        if application_channel:
            channel = bot.get_channel(application_channel)
            if channel:
                # Clear existing messages
                async for message in channel.history(limit=100):
                    if message.author == bot.user:
                        await message.delete()
                # Create new application panel
                await send_application_embed(channel)
                logging.info("Recreated application dropdown menu")

        # Recreate reaction role message if channel exists
        # Make sure reaction_message_id and reaction_channel_id are defined
        if 'reaction_message_id' not in globals() or 'reaction_channel_id' not in globals():
            global reaction_message_id, reaction_channel_id
            reaction_message_id = None
            reaction_channel_id = None
            logging.warning("Reaction role variables were not properly initialized")

        # Debug logging for already loaded reaction role data
        logging.info(f"Using already loaded reaction role data: message_id={reaction_message_id}, channel_id={reaction_channel_id}")
        logging.info(f"Reaction roles structure: {reaction_roles}")

        if reaction_channel_id and reaction_message_id:
            try:
                channel = bot.get_channel(reaction_channel_id)
                if channel:
                    logging.info(f"Found channel for reaction roles: {channel.name}")

                    # Try to get the existing message
                    try:
                        message = await channel.fetch_message(reaction_message_id)
                        logging.info(f"Found existing reaction message: {message.id}")
                        # If message exists, clear reactions and recreate
                        await message.clear_reactions()
                    except discord.NotFound:
                        logging.info("Reaction message not found, creating a new one")
                        # If message doesn't exist, create a new one
                        embed = discord.Embed(
                            title="Reaction Roles",
                            description="React to get roles!",
                            color=discord.Color.blue()
                        )
                        message = await channel.send(embed=embed)
                        reaction_message_id = message.id
                        await save_data()

                    # Add fields and reactions for each role
                    logging.info(f"Reaction roles content: {reaction_roles}")

                    # Create a new reaction_roles structure with the correct format
                    # We'll manually extract the emoji and role IDs from the nested structure
                    fixed_roles = {}

                    # Use the already loaded reaction_roles data
                    logging.info(f"Using loaded reaction_roles: {reaction_roles}")

                    # Use the roles from the already loaded reaction_roles
                    fixed_roles = reaction_roles.get("roles", {})
                    logging.info(f"Using reaction roles: {fixed_roles}")

                    # Now proceed with the embed creation
                    embed = message.embeds[0]
                    embed.clear_fields()

                    for emoji_str, role_id in fixed_roles.items():
                        logging.info(f"Processing role: emoji={emoji_str}, role_id={role_id}, type={type(role_id)}")

                        # Convert role_id to int if it's a string
                        if isinstance(role_id, str) and role_id.isdigit():
                            role_id = int(role_id)

                        role = channel.guild.get_role(role_id)
                        if role:
                            logging.info(f"Found role: {role.name}")
                            embed.add_field(name=f"{emoji_str} {role.name}", value="React to get this role!", inline=False)
                            await message.add_reaction(emoji_str)
                        else:
                            logging.warning(f"Role not found for ID: {role_id}")

                    await message.edit(embed=embed)
                    logging.info("Recreated reaction role message")
                else:
                    logging.warning(f"Channel not found for reaction roles: {reaction_channel_id}")
            except Exception as e:
                logging.error(f"Error recreating reaction role message: {e}")
                import traceback
                traceback.print_exc()

        log_bot_status("Bot initialization completed!", "SUCCESS")
        logging.info("Bot initialization completed!")

    except Exception as e:
        log_error_to_console(e, "bot initialization")
        logging.error(f"Error during bot initialization: {e}")
        import traceback
        traceback.print_exc()

# Optimized message handler with sticky message caching
# Cache for sticky messages to avoid repeated DB lookups
sticky_message_cache = {}
sticky_message_last_check = {}
STICKY_CHECK_INTERVAL = 60  # seconds between cache refreshes

@bot.event
async def on_message(message):
    """
    Unified message handler with optimized performance
    - Handles commands
    - Manages sticky messages with caching
    - Processes webhook messages
    """
    try:
        # Ignore messages from the bot itself
        if message.author == bot.user:
            return

        # Process commands first
        await bot.process_commands(message)

        # Handle sticky messages with optimized performance
        channel_id = message.channel.id
        current_time = time.time()

        # Check if we need to refresh the sticky message cache for this channel
        if (channel_id not in sticky_message_last_check or
            current_time - sticky_message_last_check.get(channel_id, 0) > STICKY_CHECK_INTERVAL):
            # Update cache from the global sticky_messages
            if channel_id in sticky_messages:
                sticky_message_cache[channel_id] = sticky_messages[channel_id]
                sticky_message_last_check[channel_id] = current_time
            else:
                # Remove from cache if no longer in sticky_messages
                if channel_id in sticky_message_cache:
                    del sticky_message_cache[channel_id]
                if channel_id in sticky_message_last_check:
                    del sticky_message_last_check[channel_id]

        # Check if this channel has a sticky message
        if channel_id in sticky_message_cache:
            sticky_message = sticky_message_cache[channel_id]

            # Use a more efficient approach to check for existing sticky messages
            # Only check the last 2 messages for better performance
            found_sticky = False
            async for msg in message.channel.history(limit=2):
                if msg.author == bot.user and msg.content == sticky_message:
                    found_sticky = True
                    break

            # Post sticky message if not found in recent messages
            if not found_sticky:
                try:
                    await message.channel.send(sticky_message)
                    logging.info(f"Reposted sticky message in {message.channel.name}")
                except discord.HTTPException as e:
                    logging.error(f"Failed to post sticky message: {e}")

        # Handle Tebex webhook messages
        if message.webhook_id and message.channel.id == tebex_channel:
            try:
                # Parse the webhook message
                content = message.content
                logging.debug(f"Received webhook content: {content[:100]}...")  # Log only first 100 chars

                if "has received a payment" in content:
                    # Process payment notification
                    await process_payment_notification(message, content)

            except Exception as e:
                logging.error(f"Error processing webhook message: {e}")
                traceback.print_exc()

    except Exception as e:
        logging.error(f"Error in on_message event: {e}")
        traceback.print_exc()

async def process_payment_notification(message, content):
    """Process Tebex payment notification webhook"""
    try:
        parts = content.split("╽")
        data = {}

        # Parse the first part (store name)
        data['store'] = parts[0].split("has received a payment")[0].strip()

        # Parse remaining parts
        for part in parts[1:]:
            if ":" in part:
                key, value = part.split(":", 1)
                key = key.strip().lower()
                value = value.strip()
                data[key] = value

        # Get current time
        current_time = datetime.now(timezone.utc)

        # Store transaction in MongoDB
        transaction_data = {
            'buyer': data.get('from', 'Unknown'),
            'item': data.get('package', 'Unknown'),
            'price': data.get('price', '$0'),
            'email': data.get('email', 'No Email'),
            'timestamp': current_time,
            'transaction_id': data.get('transaction id', '0'),
            'chargeback': False
        }

        # Save transaction to MongoDB using the imported function
        await save_transaction(transaction_data)

        # Create and send embed
        embed = discord.Embed(
            title="Transaction Notification",
            description="**Payment Successfully Processed**\n\nA new transaction has been completed and recorded in our secure payment system.",
            color=0x2B2D31,
            timestamp=current_time
        )

        # Add structured transaction details
        embed.add_field(
            name="📋 Transaction Details",
            value=f"**Transaction ID:** `{data.get('transaction id', '0')}`\n**Amount:** `{data.get('price', '$0')}`\n**Product:** `{data.get('package', 'Unknown')}`",
            inline=True
        )

        embed.add_field(
            name="👤 Customer Information",
            value=f"**Name:** `{data.get('from', 'Unknown')}`\n**Email:** `{data.get('email', 'No Email')}`",
            inline=True
        )

        embed.add_field(
            name="⏰ Processing Time",
            value=f"**Completed:** {current_time.strftime('%B %d, %Y')}\n**Time:** {current_time.strftime('%I:%M %p UTC')}",
            inline=False
        )

        # Set professional footer
        embed.set_footer(
            text=f"Secure Payment Processing System • {current_time.strftime('%m/%d/%Y %I:%M %p')}",
            icon_url=message.guild.icon.url if message.guild.icon else None
        )

        # Try to delete the webhook message after sending the formatted one
        try:
            await message.delete()
        except discord.NotFound:
            logging.warning("Webhook message already deleted or not found")
        except Exception as e:
            logging.error(f"Failed to delete webhook message: {e}")

        # Send the formatted message
        channel = bot.get_channel(tebex_channel)
        if channel:
            await channel.send(embed=embed)
            console_log("Payment notification processed", "SUCCESS")
        else:
            logging.error(f"Could not find channel with ID {tebex_channel}")

    except Exception as e:
        logging.error(f"Error processing payment notification: {e}")
        traceback.print_exc()

@bot.event
async def on_member_join(member_obj):
    """Handle new member joins with welcome message and auto-role"""
    try:
        # Send welcome message if configured
        if welcome_channel_id:
            channel = bot.get_channel(welcome_channel_id)
            if channel:
                embed = discord.Embed(
                    title="Welcome!",
                    description=welcome_message,
                    color=discord.Color.blue()
                )
                if welcome_image_url:
                    embed.set_image(url=welcome_image_url)
                await channel.send(content=f"Welcome {member_obj.mention}!", embed=embed)
                logging.debug(f"Sent welcome message for {member_obj.name}")

        # Assign auto-role if configured
        if join_role_id:
            role = member_obj.guild.get_role(join_role_id)
            if role:
                await member_obj.add_roles(role)
                console_log(f"Auto-role assigned: {role.name} → {member_obj.name}", "SUCCESS")
            else:
                logging.warning(f"Join role with ID {join_role_id} not found in guild {member_obj.guild.name}")
    except Exception as e:
        logging.error(f"Error in on_member_join: {e}")
        traceback.print_exc()

@bot.event
async def on_raw_reaction_add(payload):
    # Ignore bot reactions
    if payload.user_id == bot.user.id:
        return

    # Check if this is a reaction role message
    if payload.message_id == reaction_message_id:
        emoji = str(payload.emoji)
        # Check if reaction_roles has the expected structure
        if "roles" in reaction_roles and emoji in reaction_roles["roles"]:
            guild = bot.get_guild(payload.guild_id)
            if not guild:
                return

            # Get role ID and convert to int if it's a string
            role_id = reaction_roles["roles"][emoji]
            if isinstance(role_id, str) and role_id.isdigit():
                role_id = int(role_id)

            role = guild.get_role(role_id)
            if not role:
                return

            member = guild.get_member(payload.user_id)
            if not member:
                return

            try:
                # If multiple roles are not allowed, remove other reaction roles first
                if "config" in reaction_roles and not reaction_roles["config"]["allow_multiple"]:
                    current_role_id = reaction_roles["roles"][emoji]

                    for emoji_key, other_role_id in reaction_roles["roles"].items():
                        # Skip the current role
                        if emoji_key == emoji:
                            continue

                        # Convert role IDs to strings for comparison if needed
                        if isinstance(current_role_id, int) and isinstance(other_role_id, str) and other_role_id.isdigit():
                            other_role_id_int = int(other_role_id)
                        elif isinstance(current_role_id, str) and current_role_id.isdigit() and isinstance(other_role_id, int):
                            current_role_id = int(current_role_id)
                            other_role_id_int = other_role_id
                        elif isinstance(other_role_id, str) and other_role_id.isdigit():
                            other_role_id_int = int(other_role_id)
                        else:
                            other_role_id_int = other_role_id

                        # Remove other roles
                        if current_role_id != other_role_id_int:
                            old_role = guild.get_role(other_role_id_int)
                            if old_role and old_role in member.roles:
                                await member.remove_roles(old_role)

                await member.add_roles(role)
            except discord.HTTPException:
                pass

@bot.event
async def on_raw_reaction_remove(payload):
    # Check if this is a reaction role message
    if payload.message_id == reaction_message_id:
        emoji = str(payload.emoji)
        # Check if reaction_roles has the expected structure
        if "roles" in reaction_roles and emoji in reaction_roles["roles"]:
            guild = bot.get_guild(payload.guild_id)
            if not guild:
                return

            # Get role ID and convert to int if it's a string
            role_id = reaction_roles["roles"][emoji]
            if isinstance(role_id, str) and role_id.isdigit():
                role_id = int(role_id)

            role = guild.get_role(role_id)
            if not role:
                return

            member = guild.get_member(payload.user_id)
            if not member:
                return

            try:
                await member.remove_roles(role)
            except discord.HTTPException:
                pass
async def restore_application_buttons():
    """Restore application buttons after bot restart"""
    try:
        logging.debug("Attempting to restore application buttons...")
        if application_log_channel:
            channel = bot.get_channel(application_log_channel)
            if channel:
                logging.debug(f"Found application log channel: {channel.name}")

                # Convert applications_status to use string keys if needed
                string_keyed_status = {}
                for user_id, status in applications_status.items():
                    string_keyed_status[str(user_id)] = status

                for user_id_str, status in string_keyed_status.items():
                    if not status.get("responded", False):
                        try:
                            user_id_int = int(user_id_str)
                            message_id = status.get("message_id")

                            if not message_id:
                                logging.warning(f"No message_id for application from user {user_id_str}")
                                continue

                            logging.debug(f"Restoring buttons for message {message_id} from user {user_id_str}")

                            try:
                                message = await channel.fetch_message(message_id)
                            except discord.NotFound:
                                logging.warning(f"Could not find message {message_id} for application")
                                continue

                            if message:
                                # Create a persistent view with a timeout of None
                                view = View(timeout=None)

                                # Create professional buttons with custom_ids to make them persistent
                                accept_button = Button(
                                    label="Approve Application",
                                    style=discord.ButtonStyle.success,
                                    custom_id=f"app_accept_{user_id_str}_{message_id}"
                                )

                                reject_button = Button(
                                    label="Decline Application",
                                    style=discord.ButtonStyle.danger,
                                    custom_id=f"app_reject_{user_id_str}_{message_id}"
                                )

                                accept_reason_button = Button(
                                    label="Approve with Feedback",
                                    style=discord.ButtonStyle.primary,
                                    custom_id=f"app_accept_reason_{user_id_str}_{message_id}"
                                )

                                reject_reason_button = Button(
                                    label="Decline with Feedback",
                                    style=discord.ButtonStyle.secondary,
                                    custom_id=f"app_reject_reason_{user_id_str}_{message_id}"
                                )

                                user = bot.get_user(user_id_int)
                                app_name = status["application_name"]

                                if not user:
                                    logging.warning(f"Could not find user with ID {user_id_int}")
                                    # Try to fetch the user if not in cache
                                    try:
                                        user = await bot.fetch_user(user_id_int)
                                    except:
                                        logging.error(f"Failed to fetch user with ID {user_id_int}")
                                        continue

                                # Define callbacks for each button
                                async def accept_callback(i):
                                    await handle_application_response(i, user, "accepted", app_name)

                                async def reject_callback(i):
                                    await handle_application_response(i, user, "rejected", app_name)

                                async def accept_with_reason_callback(i):
                                    if applications_status.get(str(user.id), {}).get("responded"):
                                        await i.response.send_message("This application has already been responded to.", ephemeral=True)
                                        return
                                    await i.response.send_modal(AcceptReasonModal(user, app_name))

                                async def reject_with_reason_callback(i):
                                    if applications_status.get(str(user.id), {}).get("responded"):
                                        await i.response.send_message("This application has already been responded to.", ephemeral=True)
                                        return
                                    await i.response.send_modal(RejectReasonModal(user, app_name))

                                accept_button.callback = accept_callback
                                reject_button.callback = reject_callback
                                accept_reason_button.callback = accept_with_reason_callback
                                reject_reason_button.callback = reject_with_reason_callback

                                view.add_item(accept_button)
                                view.add_item(reject_button)
                                view.add_item(accept_reason_button)
                                view.add_item(reject_reason_button)

                                await message.edit(view=view)
                                logging.debug(f"Successfully restored buttons for message {message_id}")
                        except Exception as e:
                            logging.error(f"Error restoring application buttons for user {user_id_str}: {e}")
                            import traceback
                            traceback.print_exc()
            else:
                logging.warning(f"Application log channel not found: {application_log_channel}")
        else:
            logging.debug("No application log channel configured")
    except Exception as e:
        logging.error(f"Error in restore_application_buttons: {e}")
        import traceback
        traceback.print_exc()

async def handle_application_response(interaction, user, status, app_name):
    """Handle application response (accept/reject) with professional formatting"""
    try:
        user_id_str = str(user.id)
        current_time = datetime.now(timezone.utc)

        # Check if application has already been responded to
        if applications_status.get(user_id_str, {}).get("responded"):
            try:
                await interaction.response.send_message("This application has already been processed.", ephemeral=True)
            except discord.errors.InteractionResponded:
                await interaction.followup.send("This application has already been processed.", ephemeral=True)
            return

        # Update application status with detailed information
        applications_status[user_id_str] = {
            "responded": True,
            "admin": interaction.user.id,
            "admin_name": f"{interaction.user.name}#{interaction.user.discriminator}",
            "status": status,
            "response_time": current_time.isoformat(),
            "application_name": app_name
        }
        await save_data()

        # Create professional response for admin
        if status == "accepted":
            admin_embed = discord.Embed(
                title="Application Approved",
                description=f"You have approved the application for {user.mention}.",
                color=0x2ECC71  # Professional green color
            )
        else:
            admin_embed = discord.Embed(
                title="Application Declined",
                description=f"You have declined the application for {user.mention}.",
                color=0x95A5A6  # Professional gray color
            )

        admin_embed.add_field(name="Application Type", value=app_name, inline=False)
        admin_embed.add_field(
            name="Note",
            value="For more detailed feedback, use the 'Approve with Feedback' or 'Decline with Feedback' options.",
            inline=False
        )
        admin_embed.set_footer(text=f"Processed at {current_time.strftime('%Y-%m-%d %H:%M:%S')} UTC")

        # Send response to admin
        try:
            await interaction.response.send_message(embed=admin_embed, ephemeral=True)
        except discord.errors.InteractionResponded:
            await interaction.followup.send(embed=admin_embed, ephemeral=True)

        # Create professional notification for applicant
        if status == "accepted":
            user_embed = discord.Embed(
                title="Application Approved",
                description=f"Congratulations! Your application for **{app_name}** has been approved.",
                color=0x2ECC71  # Professional green color
            )

            # Add next steps section
            user_embed.add_field(
                name="Next Steps",
                value="• A staff member will contact you with further instructions\n• Please ensure your DMs are open\n• Contact staff if you have any questions",
                inline=False
            )
        else:
            user_embed = discord.Embed(
                title="Application Status Update",
                description=f"Thank you for your interest in the **{app_name}** position. After review, we regret to inform you that your application has not been approved at this time.",
                color=0x95A5A6  # Professional gray color
            )

            # Add encouragement section
            user_embed.add_field(
                name="Next Steps",
                value="• We encourage you to apply again in the future\n• Consider requesting feedback to improve your next application\n• Thank you for your interest in our community",
                inline=False
            )

        # Add footer with timestamp
        user_embed.set_footer(text=f"© {datetime.now().year} Application System")
        user_embed.timestamp = current_time

        # Send notification to applicant
        try:
            await user.send(embed=user_embed)
            console_log(f"Application {status}: {user.name} ({app_name})", "SUCCESS")
        except discord.errors.Forbidden:
            logging.warning(f"Could not send DM to user {user.id} - DMs may be disabled")
        except Exception as dm_error:
            logging.error(f"Error sending DM to user {user.id}: {dm_error}")

    except discord.errors.InteractionResponded:
        # This is a fallback in case the interaction was already responded to
        logging.warning("Interaction was already responded to")
        try:
            await interaction.followup.send("Processing your response...", ephemeral=True)
        except:
            pass
    except Exception as e:
        logging.error(f"Error handling application response: {e}")
        try:
            await interaction.response.send_message("An error occurred while processing your response.", ephemeral=True)
        except discord.errors.InteractionResponded:
            try:
                await interaction.followup.send("An error occurred while processing your response.", ephemeral=True)
            except:
                pass


async def create_backup():
    try:
        timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
        backup_path = f"backups/backup_{timestamp}.json"

        # Create backups directory if it doesn't exist
        os.makedirs("backups", exist_ok=True)

        # Check if the data file exists
        if not os.path.exists(DATA_FILE_PATH):
            # Create an empty data file
            with open(DATA_FILE_PATH, 'w') as f:
                json.dump({}, f)
            logging.info(f"Created empty data file at {DATA_FILE_PATH}")
            data = {}
        else:
            # Load data from existing file
            with open(DATA_FILE_PATH, 'r') as source:
                data = json.load(source)

        # Create the backup
        with open(backup_path, 'w') as backup:
            json.dump(data, backup, indent=4)

        logging.info(f"Backup created: {backup_path}")
    except Exception as e:
        logging.error(f"Backup failed: {e}")

@bot.event
async def on_interaction(interaction: discord.Interaction):
    if interaction.type == discord.InteractionType.component:
        try:
            custom_id = interaction.data.get("custom_id", "")

            # Handle basic ticket creation button
            if custom_id == "create_ticket":
                # Create category selection buttons
                view = View()

                # Get available categories
                categories = ticket_config.get("categories", {})
                if not categories:
                    await interaction.response.send_message("No ticket categories are configured!", ephemeral=True)
                    return

                # Create a button for each category
                for category_id, category_info in categories.items():
                    button = Button(
                        label=category_info["name"],
                        custom_id=f"ticket_category_{category_id}",
                        style=discord.ButtonStyle.primary
                    )
                    view.add_item(button)

                # Send category selection message
                await interaction.response.send_message(
                    "Please select a ticket category:",
                    view=view,
                    ephemeral=True
                )
                return

            # Handle ticket category button selections
            elif custom_id.startswith("ticket_category_"):
                try:
                    # Extract category_id and keep it as string
                    category_id = custom_id.split("_")[-1]

                    # Debug prints
                    print(f"Received ticket category interaction for category: {category_id}")
                    print(f"Available categories: {ticket_config.get('categories', {})}")

                    # Verify category exists using string comparison
                    if category_id not in ticket_config.get("categories", {}):
                        await interaction.response.send_message("Invalid ticket category! Please contact an administrator.", ephemeral=True)
                        return

                    # Convert to int only when passing to create_ticket
                    await create_ticket(interaction, int(category_id))
                    return
                except ValueError as e:
                    print(f"Error parsing category ID: {e}")
                    await interaction.response.send_message("Invalid ticket category format.", ephemeral=True)
                    return
                except Exception as e:
                    print(f"Error creating ticket: {e}")
                    await interaction.response.send_message("An error occurred while creating the ticket.", ephemeral=True)
                    return

            # Handle dropdown menu selections for tickets
            elif custom_id in ["ticket_category_select", "ticket_category_dropdown_v2"] and "values" in interaction.data:
                try:
                    # Extract category_id from the dropdown selection
                    category_id = interaction.data["values"][0]

                    # Debug logging
                    print(f"Processing ticket category from dropdown: {category_id}")
                    print(f"Available categories: {ticket_config.get('categories', {})}")

                    # Convert both to strings for comparison
                    categories = {str(k): v for k, v in ticket_config.get("categories", {}).items()}

                    # Verify category exists
                    if category_id not in categories:
                        print(f"Category {category_id} not found in config")
                        await interaction.response.send_message(
                            "This ticket category no longer exists. Please contact an administrator.",
                            ephemeral=True
                        )
                        return

                    # Convert to int for create_ticket function
                    await create_ticket(interaction, int(category_id))
                    return
                except ValueError as e:
                    print(f"Error parsing category ID from dropdown: {e}")
                    await interaction.response.send_message("Invalid ticket category format.", ephemeral=True)
                    return
                except Exception as e:
                    print(f"Error creating ticket from dropdown: {e}")
                    traceback.print_exc()
                    await interaction.response.send_message("An error occurred while creating the ticket.", ephemeral=True)
                    return

            # Handle application button interactions
            if custom_id.startswith("app_accept_") and not custom_id.startswith("app_accept_reason_"):
                parts = custom_id.split("_")
                if len(parts) >= 3:
                    user_id = parts[2]
                    user = await bot.fetch_user(int(user_id))
                    if user:
                        app_name = applications_status.get(str(user_id), {}).get("application_name", "Unknown")
                        await handle_application_response(interaction, user, "accepted", app_name)
                    else:
                        await interaction.response.send_message("Could not find user for this application.", ephemeral=True)
                return

            elif custom_id.startswith("app_reject_") and not custom_id.startswith("app_reject_reason_"):
                parts = custom_id.split("_")
                if len(parts) >= 3:
                    user_id = parts[2]
                    user = await bot.fetch_user(int(user_id))
                    if user:
                        app_name = applications_status.get(str(user_id), {}).get("application_name", "Unknown")
                        await handle_application_response(interaction, user, "rejected", app_name)
                    else:
                        await interaction.response.send_message("Could not find user for this application.", ephemeral=True)
                return

            elif custom_id.startswith("app_accept_reason_"):
                parts = custom_id.split("_")
                if len(parts) >= 4:
                    user_id = parts[3]
                    user = await bot.fetch_user(int(user_id))
                    if user:
                        app_name = applications_status.get(str(user_id), {}).get("application_name", "Unknown")
                        await interaction.response.send_modal(AcceptReasonModal(user, app_name))
                    else:
                        await interaction.response.send_message("Could not find user for this application.", ephemeral=True)
                return

            elif custom_id.startswith("app_reject_reason_"):
                parts = custom_id.split("_")
                if len(parts) >= 4:
                    user_id = parts[3]
                    user = await bot.fetch_user(int(user_id))
                    if user:
                        app_name = applications_status.get(str(user_id), {}).get("application_name", "Unknown")
                        await interaction.response.send_modal(RejectReasonModal(user, app_name))
                    else:
                        await interaction.response.send_message("Could not find user for this application.", ephemeral=True)
                return

            elif custom_id.startswith("app_create_ticket_"):
                parts = custom_id.split("_")
                if len(parts) >= 4:
                    user_id = parts[3]
                    try:
                        user = await bot.fetch_user(int(user_id))
                        if user:
                            app_name = applications_status.get(str(user_id), {}).get("application_name", "Unknown")

                            # Create category selection buttons for ticket creation
                            view = View()
                            categories = ticket_config.get("categories", {})

                            if not categories:
                                await interaction.response.send_message(
                                    "No ticket categories are configured! Please contact an administrator.",
                                    ephemeral=True
                                )
                                return

                            # Create a button for each category with professional styling
                            for category_id, category_info in categories.items():
                                button = Button(
                                    label=category_info["name"],
                                    custom_id=f"app_ticket_cat_{user_id}_{category_id}",
                                    style=discord.ButtonStyle.primary,
                                    emoji="🎫"
                                )
                                view.add_item(button)

                            # Create professional embed for category selection matching main ticket system
                            embed = discord.Embed(
                                title="🎫 Create Application Ticket",
                                description=(
                                    f"Creating a support ticket for **{user.display_name}**'s {app_name} application.\n\n"
                                    f"Please select the appropriate category below to proceed with ticket creation."
                                ),
                                color=0x000000  # Professional black theme matching main system
                            )

                            # Add elegant separator for visual hierarchy (matching main system)
                            embed.add_field(
                                name="\u200b",  # Invisible field name for spacing
                                value="━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━",
                                inline=False
                            )

                            # Add application context section with professional formatting
                            embed.add_field(
                                name="**Application Context**",
                                value=f"**Applicant:** {user.display_name} (@{user.name})\n**Application Type:** {app_name}",
                                inline=False
                            )

                            # Add elegant separator before categories (matching main system)
                            embed.add_field(
                                name="\u200b",  # Invisible field name for spacing
                                value="━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━",
                                inline=False
                            )

                            # Add category descriptions with professional formatting
                            embed.add_field(
                                name="**Available Categories**",
                                value="Select the most appropriate category for this application follow-up:",
                                inline=False
                            )

                            for category_id, category_info in categories.items():
                                embed.add_field(
                                    name=f"🎫 **{category_info['name']}**",
                                    value=f"```{category_info.get('description', 'No description available')}```",
                                    inline=False
                                )

                            # Add elegant separator at the bottom (matching main system)
                            embed.add_field(
                                name="\u200b",  # Invisible field name for spacing
                                value="━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━",
                                inline=False
                            )

                            # Add professional footer with timestamp
                            current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
                            embed.set_footer(text=f"Application Ticket Creation • {current_time}")

                            await interaction.response.send_message(
                                embed=embed,
                                view=view,
                                ephemeral=True
                            )
                        else:
                            await interaction.response.send_message("Could not find user for this application.", ephemeral=True)
                    except Exception as e:
                        logging.error(f"Error creating ticket for application: {e}")
                        await interaction.response.send_message("An error occurred while creating the ticket.", ephemeral=True)
                return

            # Handle application ticket category selection
            elif custom_id.startswith("app_ticket_cat_"):
                parts = custom_id.split("_")
                if len(parts) >= 5:
                    user_id = parts[3]
                    category_id = parts[4]
                    try:
                        user = await bot.fetch_user(int(user_id))
                        app_name = applications_status.get(str(user_id), {}).get("application_name", "Unknown")

                        # Create the ticket with application context
                        from tickets import create_application_ticket
                        success = await create_application_ticket(interaction, int(category_id), user, app_name)

                        if success:
                            await interaction.response.send_message(
                                f"✅ Ticket created successfully for {user.display_name}'s {app_name} application!",
                                ephemeral=True
                            )
                        else:
                            await interaction.response.send_message(
                                "❌ Failed to create ticket. Please try again.",
                                ephemeral=True
                            )
                    except Exception as e:
                        logging.error(f"Error creating application ticket: {e}")
                        await interaction.response.send_message("An error occurred while creating the ticket.", ephemeral=True)
                return

            # Handle ticket interactions
            elif custom_id == "create_ticket":
                try:
                    # Create category selection buttons
                    view = View()

                    # Get available categories and ensure it exists
                    categories = ticket_config.get("categories", {})
                    if not categories:
                        await interaction.response.send_message(
                            "No ticket categories are configured! Please contact an administrator.",
                            ephemeral=True
                        )
                        return

                    # Debug log
                    print(f"Available categories when creating buttons: {categories}")

                    # Create a button for each category
                    for category_id, category_info in categories.items():
                        button = Button(
                            label=category_info["name"],
                            # Store category_id as string in custom_id
                            custom_id=f"ticket_category_{str(category_id)}",
                            style=discord.ButtonStyle.primary
                        )
                        view.add_item(button)

                    # Create an embed for category selection
                    embed = discord.Embed(
                        title="🎫 Create a Ticket",
                        description="Please select a category for your ticket:",
                        color=discord.Color.blue()
                    )

                    # Add category descriptions to embed
                    for category_id, category_info in categories.items():
                        embed.add_field(
                            name=category_info["name"],
                            value=category_info.get("description", "No description available"),
                            inline=False
                        )

                    await interaction.response.send_message(
                        embed=embed,
                        view=view,
                        ephemeral=True
                    )

                except Exception as e:
                    print(f"Error creating ticket selection: {e}")
                    await interaction.response.send_message(
                        "An error occurred while creating the ticket selection. Please try again later.",
                        ephemeral=True
                    )

            elif custom_id.startswith("ticket_category_"):
                try:
                    # Extract category_id as string
                    category_id = custom_id.split("ticket_category_")[-1]

                    # Debug logging
                    print(f"Processing ticket category: {category_id}")
                    print(f"Available categories: {ticket_config.get('categories', {})}")

                    # Convert both to strings for comparison
                    categories = {str(k): v for k, v in ticket_config.get("categories", {}).items()}

                    # Verify category exists
                    if category_id not in categories:
                        print(f"Category {category_id} not found in config")
                        await interaction.response.send_message(
                            "This ticket category no longer exists. Please contact an administrator.",
                            ephemeral=True
                        )
                        return

                    # Convert to int for create_ticket function
                    await create_ticket(interaction, int(category_id))
                    return

                except ValueError as e:
                    print(f"Error parsing category ID: {e}")
                    await interaction.response.send_message("Invalid ticket category format.", ephemeral=True)
                    return
                except Exception as e:
                    print(f"Error creating ticket: {e}")
                    await interaction.response.send_message("An error occurred while creating the ticket.", ephemeral=True)
                    return

            elif custom_id == "close_ticket":
                try:
                    await interaction.response.defer(ephemeral=True)
                    success, error = await close_ticket(interaction.channel_id, closer=interaction.user)
                    if not success:
                        await interaction.followup.send(f"Error closing ticket: {error}", ephemeral=True)
                except Exception as e:
                    print(f"Error closing ticket: {e}")
                    try:
                        await interaction.followup.send(
                            "An error occurred while closing the ticket. Please try again.",
                            ephemeral=True
                        )
                    except discord.NotFound:
                        pass

            elif custom_id == "close_with_reason":
                try:
                    # Check if user has staff role
                    has_staff_role = False
                    for role_id in ticket_config.get("staff_roles", []):
                        role = interaction.guild.get_role(role_id)
                        if role and role in interaction.user.roles:
                            has_staff_role = True
                            break

                    if not has_staff_role:
                        await interaction.response.send_message(
                            "Only staff members can close tickets with reasons.",
                            ephemeral=True
                        )
                        return

                    # Import and use the new CloseWithReasonModal
                    from tickets import CloseWithReasonModal
                    modal = CloseWithReasonModal()
                    await interaction.response.send_modal(modal)

                except Exception as e:
                    print(f"Error handling close with reason: {e}")
                    await interaction.response.send_message(
                        "An error occurred while processing your request.",
                        ephemeral=True
                    )

            elif custom_id == "reopen_ticket":
                try:
                    await interaction.response.defer(ephemeral=True)

                    # Check if user has staff role
                    has_staff_role = False
                    for role_id in ticket_config["staff_roles"]:
                        role = interaction.guild.get_role(role_id)
                        if role and role in interaction.user.roles:
                            has_staff_role = True
                            break

                    if not has_staff_role:
                        await interaction.followup.send("You don't have permission to reopen tickets.", ephemeral=True)
                        return

                    success, error = await reopen_ticket(interaction.channel_id, interaction.user)
                    if not success:
                        await interaction.followup.send(f"Error reopening ticket: {error}", ephemeral=True)


                except Exception as e:
                    print(f"Error reopening ticket: {e}")
                    try:
                        await interaction.followup.send(
                            "An error occurred while reopening the ticket. Please try again.",
                            ephemeral=True
                        )
                    except discord.NotFound:
                        pass

            elif custom_id == "view_transcript_channel":
                try:
                    # Import and use the new channel transcript handler
                    from tickets import handle_transcript_button_channel
                    await handle_transcript_button_channel(interaction)
                except Exception as e:
                    print(f"Error viewing transcript in channel: {e}")
                    try:
                        if not interaction.response.is_done():
                            await interaction.response.send_message(
                                "An error occurred while viewing the transcript.",
                                ephemeral=True
                            )
                        else:
                            await interaction.followup.send(
                                "An error occurred while viewing the transcript.",
                                ephemeral=True
                            )
                    except discord.NotFound:
                        pass

            elif custom_id == "claim_ticket":
                try:
                    # Check if user has staff role
                    has_staff_role = False
                    for role_id in ticket_config["staff_roles"]:
                        role = interaction.guild.get_role(role_id)
                        if role and role in interaction.user.roles:
                            has_staff_role = True
                            break

                    if not has_staff_role:
                        await interaction.response.send_message("You don't have permission to claim tickets.", ephemeral=True)
                        return

                    success, error = await claim_ticket(interaction.channel_id, interaction.user)
                    if not success:
                        await interaction.response.send_message(f"Error claiming ticket: {error}", ephemeral=True)
                    else:
                        await interaction.response.send_message("Ticket claimed successfully!", ephemeral=True)
                except Exception as e:
                    print(f"Error claiming ticket: {e}")
                    await interaction.response.send_message("An error occurred while claiming the ticket.", ephemeral=True)

        except Exception as e:
            print(f"Error in interaction handler: {e}")
            try:
                await interaction.response.send_message("An error occurred while processing your request.", ephemeral=True)
            except:
                pass

if __name__ == "__main__":
    bot.run(token)