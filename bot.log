2025-02-28 21:50:48,082 - INFO - logging in using static token
2025-02-28 21:50:50,662 - INFO - Shard ID None has connected to Gateway (Session ID: 0e2da6bb27a7fc49aada7b48f4d8a9a5).
2025-02-28 21:51:16,210 - INFO - Data saved successfully to MongoDB
2025-02-28 21:52:07,359 - INFO - Permission Check - Command: setup_reaction_roles
2025-02-28 21:52:07,359 - INFO - User: crimsonop69#0 (ID: 567939816721874957)
2025-02-28 21:52:07,359 - INFO - Guild: CRIMSON GAMING (ID: 656611572021592064)
2025-02-28 21:52:07,359 - INFO - Has Admin Permission: True
2025-02-28 21:52:17,447 - INFO - Data saved successfully to MongoDB
2025-02-28 21:57:21,726 - INFO - logging in using static token
2025-02-28 21:57:24,786 - INFO - Shard ID None has connected to Gateway (Session ID: f2c4abc1d2791cb06060e67aabfed3d1).
2025-02-28 21:58:23,520 - INFO - Data saved successfully to MongoDB
2025-02-28 21:58:48,274 - INFO - Permission Check - Command: setup_reaction_roles
2025-02-28 21:58:48,274 - INFO - User: crimsonop69#0 (ID: 567939816721874957)
2025-02-28 21:58:48,274 - INFO - Guild: CRIMSON GAMING (ID: 656611572021592064)
2025-02-28 21:58:48,274 - INFO - Has Admin Permission: True
2025-02-28 21:58:57,623 - INFO - Data saved successfully to MongoDB
2025-02-28 21:59:13,656 - INFO - Data saved successfully to MongoDB
2025-03-01 11:52:35,852 - INFO - logging in using static token
2025-03-01 11:52:38,009 - INFO - Shard ID None has connected to Gateway (Session ID: efc2f16df9168b78d89c349882e736ba).
2025-03-01 11:53:41,178 - INFO - logging in using static token
2025-03-01 11:53:43,589 - INFO - Shard ID None has connected to Gateway (Session ID: 90707bc5de9fdd561d2dc260acb4ceef).
2025-03-01 11:54:13,623 - ERROR - Error in VanityNotificationModal.on_submit: 'coroutine' object is not subscriptable
2025-03-01 11:55:19,495 - INFO - logging in using static token
2025-03-01 11:55:21,600 - INFO - Shard ID None has connected to Gateway (Session ID: 970363fe19fdabcebcc6465c09bc88ee).
2025-03-01 11:58:15,264 - INFO - logging in using static token
2025-03-01 11:58:17,532 - INFO - Shard ID None has connected to Gateway (Session ID: 4dd3c92c3eb6208bf0dcbc7cea94337e).
2025-03-01 11:58:51,369 - INFO - Data saved successfully to MongoDB
2025-03-01 11:59:10,176 - INFO - Permission Check - Command: setup_reaction_roles
2025-03-01 11:59:10,176 - INFO - User: crimsonop69#0 (ID: 567939816721874957)
2025-03-01 11:59:10,176 - INFO - Guild: CRIMSON GAMING (ID: 656611572021592064)
2025-03-01 11:59:10,176 - INFO - Has Admin Permission: True
2025-03-01 11:59:17,320 - INFO - Data saved successfully to MongoDB
2025-03-01 12:00:28,710 - INFO - logging in using static token
2025-03-01 12:00:31,131 - INFO - Shard ID None has connected to Gateway (Session ID: 7d18ccd5672ef8807ff279da41d57c8e).
2025-03-01 12:07:13,790 - INFO - logging in using static token
2025-03-01 12:07:16,164 - INFO - Shard ID None has connected to Gateway (Session ID: f1fa7319398cb1e6e157a18b4ca3fdfc).
2025-03-01 12:10:00,360 - INFO - logging in using static token
2025-03-01 12:10:02,875 - INFO - Shard ID None has connected to Gateway (Session ID: d6f486d7ae1651517a5269fcc5345043).
2025-03-01 12:10:32,193 - INFO - Data saved successfully to MongoDB
2025-03-01 12:13:22,719 - INFO - logging in using static token
2025-03-01 12:13:29,177 - INFO - Shard ID None has connected to Gateway (Session ID: 3bccef44b712a301f031ff80894dbf5b).
2025-03-01 12:13:45,451 - INFO - Data saved successfully to MongoDB
2025-03-01 23:34:36,255 - INFO - logging in using static token
2025-03-01 23:34:38,894 - INFO - Shard ID None has connected to Gateway (Session ID: 36c9510ec2778533944d190cab5b7ede).
2025-03-01 23:35:56,354 - INFO - Data saved successfully to MongoDB
2025-03-01 23:36:13,666 - INFO - Data saved successfully to MongoDB
2025-03-01 23:36:29,914 - INFO - Data saved successfully to MongoDB
2025-03-01 23:38:56,784 - INFO - logging in using static token
2025-03-01 23:38:59,290 - INFO - Shard ID None has connected to Gateway (Session ID: 8e93ae9e70988fad351bc8b66939c80b).
2025-03-01 23:46:36,982 - INFO - logging in using static token
2025-03-01 23:46:39,577 - INFO - Shard ID None has connected to Gateway (Session ID: 53ad2aa88ecf4450acfe1dbc69ebdb8d).
2025-03-01 23:46:53,953 - INFO - Data saved successfully to MongoDB
2025-03-01 23:47:09,873 - INFO - Data saved successfully to MongoDB
2025-03-01 23:47:20,544 - INFO - Data saved successfully to MongoDB
2025-03-01 23:48:32,971 - INFO - logging in using static token
2025-03-01 23:48:35,383 - INFO - Shard ID None has connected to Gateway (Session ID: 67ffd2144456efcbed311876d0c134b3).
2025-03-01 23:53:44,668 - INFO - logging in using static token
2025-03-01 23:53:47,481 - INFO - Shard ID None has connected to Gateway (Session ID: 7c34183857a7837538db28325be6bd9c).
2025-03-01 23:54:22,947 - INFO - Data saved successfully to MongoDB
2025-03-01 23:54:35,552 - INFO - logging in using static token
2025-03-01 23:54:37,890 - INFO - Shard ID None has connected to Gateway (Session ID: d7e3aff38910f7cebc312336de38f648).
2025-03-02 00:12:51,068 - INFO - Shard ID None has successfully RESUMED session d7e3aff38910f7cebc312336de38f648.
2025-03-02 00:16:36,066 - INFO - logging in using static token
2025-03-02 00:16:38,904 - INFO - Shard ID None has connected to Gateway (Session ID: 2a3f838197086b40083b11cb8ad855fc).
2025-03-02 00:19:51,302 - INFO - logging in using static token
2025-03-02 00:19:55,388 - INFO - Shard ID None has connected to Gateway (Session ID: 5c59ceabe0f87a1fd4da0e83225ad5a8).
2025-03-02 00:21:31,399 - INFO - logging in using static token
2025-03-02 00:21:33,720 - INFO - Shard ID None has connected to Gateway (Session ID: 4434011a527c50ce307f22d729dfe8d5).
2025-03-02 00:21:54,195 - INFO - Data saved successfully to MongoDB
2025-03-02 00:22:07,087 - INFO - logging in using static token
2025-03-02 00:22:10,183 - INFO - Shard ID None has connected to Gateway (Session ID: 4328c1a4aa46e027b3b192a49e52dda4).
2025-03-02 00:28:43,599 - INFO - logging in using static token
2025-03-02 00:28:46,289 - INFO - Shard ID None has connected to Gateway (Session ID: 31f72bdf6d12b20a0995e48c2235593e).
2025-03-02 00:29:32,096 - INFO - logging in using static token
2025-03-02 00:29:34,239 - INFO - Shard ID None has connected to Gateway (Session ID: 7d98854e1957d6205ba1231236fd3982).
2025-03-02 00:40:18,735 - INFO - logging in using static token
2025-03-02 00:40:21,562 - INFO - Shard ID None has connected to Gateway (Session ID: 06f74f278a199d3fd1a793de552928e1).
2025-03-02 00:42:59,032 - INFO - logging in using static token
2025-03-02 00:43:01,441 - INFO - Shard ID None has connected to Gateway (Session ID: da09c7029f2f886b48fc17c412d0e0d5).
2025-03-02 00:44:26,365 - INFO - logging in using static token
2025-03-02 00:44:28,677 - INFO - Shard ID None has connected to Gateway (Session ID: 12a83caf1968bee8a733a23d9a1391fd).
2025-03-02 00:44:59,137 - INFO - Data saved successfully to MongoDB
2025-03-02 16:12:12,028 - INFO - logging in using static token
2025-03-02 16:12:15,416 - INFO - Shard ID None has connected to Gateway (Session ID: f1455bce4ea0e4d3f6e83da6ae22a305).
2025-03-02 16:13:09,695 - INFO - logging in using static token
2025-03-02 16:13:12,283 - INFO - Shard ID None has connected to Gateway (Session ID: ba1c11c8a88e6aeaeca3c7fef34591d5).
2025-03-02 16:26:49,131 - INFO - logging in using static token
2025-03-02 16:26:51,735 - INFO - Shard ID None has connected to Gateway (Session ID: 76386cea001e8629ab32394008a344a9).
2025-03-02 16:27:38,959 - INFO - Permission Check - Command: setup_reaction_roles
2025-03-02 16:27:38,959 - INFO - User: crimsonop69#0 (ID: 567939816721874957)
2025-03-02 16:27:38,959 - INFO - Guild: CRIMSON GAMING (ID: 656611572021592064)
2025-03-02 16:27:38,959 - INFO - Has Admin Permission: True
2025-03-02 16:28:04,812 - INFO - Data saved successfully to MongoDB
2025-03-02 16:28:28,930 - INFO - logging in using static token
2025-03-02 16:28:31,282 - INFO - Shard ID None has connected to Gateway (Session ID: dff18557dc4d85289cb91a5655bb70ba).
2025-03-02 16:31:41,011 - INFO - logging in using static token
2025-03-02 16:31:43,315 - INFO - Shard ID None has connected to Gateway (Session ID: e90ccbdcc915246ae4e3ea4c979bfd5b).
2025-03-04 00:20:20,428 - INFO - logging in using static token
2025-03-04 00:20:23,174 - INFO - Shard ID None has connected to Gateway (Session ID: de28b81c4420a21109e24174dfbbc49e).
2025-03-04 00:23:03,998 - INFO - logging in using static token
2025-03-04 00:23:06,614 - INFO - Shard ID None has connected to Gateway (Session ID: dd95dd7741d01ca6b69f3fda368afb07).
2025-03-04 00:23:27,015 - INFO - Permission Check - Command: setup_reaction_roles
2025-03-04 00:23:27,015 - INFO - User: crimsonop69#0 (ID: 567939816721874957)
2025-03-04 00:23:27,015 - INFO - Guild: CRIMSON GAMING (ID: 656611572021592064)
2025-03-04 00:23:27,015 - INFO - Has Admin Permission: True
2025-03-04 00:23:38,878 - INFO - Data saved successfully to MongoDB
2025-03-04 00:31:25,559 - INFO - logging in using static token
2025-03-04 00:31:28,230 - INFO - Shard ID None has connected to Gateway (Session ID: c226a72ff380575630b304f09f3406f1).
2025-03-04 00:33:08,848 - INFO - logging in using static token
2025-03-04 00:33:11,451 - INFO - Shard ID None has connected to Gateway (Session ID: 00993acdc2c15bd0eb602bd705c1b802).
2025-03-04 00:33:32,829 - INFO - logging in using static token
2025-03-04 00:33:35,206 - INFO - Shard ID None has connected to Gateway (Session ID: d32f288c7ebbee8c17a25808f8d663fd).
2025-03-04 00:34:37,599 - INFO - Shard ID None has successfully RESUMED session d32f288c7ebbee8c17a25808f8d663fd.
2025-03-04 00:40:26,682 - INFO - logging in using static token
2025-03-04 00:40:29,380 - INFO - Shard ID None has connected to Gateway (Session ID: fa72fcaa23a4197a527e347d21873c14).
2025-03-04 00:40:52,042 - INFO - Permission Check - Command: setup_reaction_roles
2025-03-04 00:40:52,042 - INFO - User: crimsonop69#0 (ID: 567939816721874957)
2025-03-04 00:40:52,042 - INFO - Guild: CRIMSON GAMING (ID: 656611572021592064)
2025-03-04 00:40:52,043 - INFO - Has Admin Permission: True
2025-03-04 00:40:59,445 - INFO - Data saved successfully to MongoDB
2025-03-04 00:44:48,342 - INFO - logging in using static token
2025-03-04 00:44:50,559 - INFO - Shard ID None has connected to Gateway (Session ID: b8d9ed8968f5c228bff27fedcfb7ba89).
2025-03-04 00:46:04,329 - INFO - Permission Check - Command: setup_reaction_roles
2025-03-04 00:46:04,330 - INFO - User: crimsonop69#0 (ID: 567939816721874957)
2025-03-04 00:46:04,330 - INFO - Guild: CRIMSON GAMING (ID: 656611572021592064)
2025-03-04 00:46:04,330 - INFO - Has Admin Permission: True
2025-03-04 00:46:17,788 - INFO - Data saved successfully to MongoDB
2025-03-04 00:46:28,482 - INFO - logging in using static token
2025-03-04 00:46:31,006 - INFO - Shard ID None has connected to Gateway (Session ID: 29397ac1b6760169fd788ba660d484a7).
2025-03-04 00:46:43,705 - ERROR - Ignoring exception in on_raw_reaction_add
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\client.py", line 449, in _run_event
    await coro(*args, **kwargs)
  File "F:\MissMinutesBotmain\bot.py", line 3206, in on_raw_reaction_add
    if emoji in reaction_roles["roles"]:
                ~~~~~~~~~~~~~~^^^^^^^^^
KeyError: 'roles'
2025-03-04 00:46:49,936 - ERROR - Ignoring exception in on_raw_reaction_remove
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\client.py", line 449, in _run_event
    await coro(*args, **kwargs)
  File "F:\MissMinutesBotmain\bot.py", line 3237, in on_raw_reaction_remove
    if emoji in reaction_roles["roles"]:
                ~~~~~~~~~~~~~~^^^^^^^^^
KeyError: 'roles'
2025-03-04 00:46:50,515 - ERROR - Ignoring exception in on_raw_reaction_add
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\client.py", line 449, in _run_event
    await coro(*args, **kwargs)
  File "F:\MissMinutesBotmain\bot.py", line 3206, in on_raw_reaction_add
    if emoji in reaction_roles["roles"]:
                ~~~~~~~~~~~~~~^^^^^^^^^
KeyError: 'roles'
2025-03-04 00:55:57,326 - INFO - logging in using static token
2025-03-04 00:55:59,999 - INFO - Shard ID None has connected to Gateway (Session ID: 834b55b62c82717eae39014e4b9e01b5).
2025-03-04 00:58:23,297 - INFO - logging in using static token
2025-03-04 00:58:25,663 - INFO - Shard ID None has connected to Gateway (Session ID: 071577352cecb3c769ad777fa03ba1ad).
2025-06-04 03:55:47,106 - root - INFO - Bot Status: Logged in as Miss Minutes#7930 (ID: 1309216318158929921)
2025-06-04 03:55:47,132 - root - INFO - Bot logged in as Miss Minutes#7930 (ID: 1309216318158929921)
2025-06-04 03:55:47,132 - root - INFO - Initializing performance optimization systems...
2025-06-04 03:55:52,185 - enhanced_database - ERROR - Failed to connect to MongoDB: 
2025-06-04 03:55:52,186 - root - INFO - Bot Status: Performance optimization initialization failed: Failed to connect to database
2025-06-04 03:55:52,186 - root - WARNING - Performance optimization initialization failed: Failed to connect to database
2025-06-04 03:55:52,186 - root - INFO - Memory manager started
2025-06-04 03:55:52,186 - root - INFO - Loading ticket configuration...
2025-06-04 03:56:22,566 - root - WARNING - Failed to load ticket configuration
2025-06-04 03:56:22,566 - root - INFO - Loading bot data...
2025-06-04 03:56:22,566 - enhanced_database - WARNING - Collection settings not found
2025-06-04 03:56:22,566 - root - INFO - No data found in enhanced database, will use regular database
2025-06-04 03:56:22,566 - root - INFO - Falling back to regular database for data loading
2025-06-04 03:56:22,566 - root - INFO - Loading bot data from MongoDB...
2025-06-04 03:56:22,567 - root - ERROR - Error in data loading: name 'pymongo' is not defined
Traceback (most recent call last):
  File "D:\MissMinutesBotmain\bot.py", line 5235, in load_data
    client = pymongo.MongoClient("mongodb://localhost:27017/")
             ^^^^^^^
NameError: name 'pymongo' is not defined
2025-06-04 03:56:22,569 - root - INFO - Bot data loaded successfully (optimized)
2025-06-04 03:56:22,569 - root - INFO - Starting command sync process (attempt 1/3)...
2025-06-04 03:56:23,482 - root - INFO - Synced 32 command(s)!
2025-06-04 03:56:23,483 - root - INFO - Started vanity status check task
2025-06-04 03:56:23,483 - root - INFO - Restored application buttons
2025-06-04 03:56:23,483 - root - INFO - Starting reaction roles panel restoration...
2025-06-04 03:56:23,484 - root - WARNING - No reaction role channel or message ID configured
2025-06-04 03:56:23,485 - root - INFO - Restored reaction roles panel
2025-06-04 03:56:23,485 - root - INFO - No expired gang invitations found
2025-06-04 03:56:23,485 - root - INFO - Cleaned up expired gang invitations
2025-06-04 03:56:23,485 - root - INFO - Starting gang invitation view restoration...
2025-06-04 03:56:23,485 - root - INFO - Found 0 pending gang invitations
2025-06-04 03:56:23,485 - root - INFO - No pending gang invitations to restore
2025-06-04 03:56:23,485 - root - INFO - Restored gang invitation views
2025-06-04 03:56:23,485 - root - INFO - Using already loaded reaction role data: message_id=None, channel_id=None
2025-06-04 03:56:23,485 - root - INFO - Reaction roles structure: {}
2025-06-04 03:56:23,485 - root - INFO - Bot Status: Bot initialization completed!
2025-06-04 03:56:23,485 - root - INFO - Bot initialization completed!
2025-06-04 03:56:28,526 - database - ERROR - Error connecting to MongoDB: localhost:27017: [WinError 10061] No connection could be made because the target machine actively refused it (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 683f6f87ed6558002f9c89e6, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [WinError 10061] No connection could be made because the target machine actively refused it (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms)')>]>
2025-06-04 03:56:28,527 - database - ERROR - Failed to connect to database
2025-06-04 03:56:33,535 - database - ERROR - Error connecting to MongoDB: localhost:27017: [WinError 10061] No connection could be made because the target machine actively refused it (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 683f6f8ced6558002f9c89e7, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [WinError 10061] No connection could be made because the target machine actively refused it (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms)')>]>
2025-06-04 03:56:33,535 - database - ERROR - Failed to connect to database
2025-06-04 03:56:38,543 - database - ERROR - Error connecting to MongoDB: localhost:27017: [WinError 10061] No connection could be made because the target machine actively refused it (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 683f6f91ed6558002f9c89e8, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [WinError 10061] No connection could be made because the target machine actively refused it (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms)')>]>
2025-06-04 03:56:38,543 - database - ERROR - Failed to connect to database
2025-06-04 03:56:43,551 - database - ERROR - Error connecting to MongoDB: localhost:27017: [WinError 10061] No connection could be made because the target machine actively refused it (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 683f6f96ed6558002f9c89e9, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [WinError 10061] No connection could be made because the target machine actively refused it (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms)')>]>
2025-06-04 03:56:43,552 - database - ERROR - Failed to connect to database
2025-06-04 03:56:48,564 - database - ERROR - Error connecting to MongoDB: localhost:27017: [WinError 10061] No connection could be made because the target machine actively refused it (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 683f6f9bed6558002f9c89ea, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [WinError 10061] No connection could be made because the target machine actively refused it (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms)')>]>
2025-06-04 03:56:48,564 - database - ERROR - Failed to connect to database
2025-06-04 03:56:53,575 - database - ERROR - Error connecting to MongoDB: localhost:27017: [WinError 10061] No connection could be made because the target machine actively refused it (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 683f6fa0ed6558002f9c89eb, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [WinError 10061] No connection could be made because the target machine actively refused it (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms)')>]>
2025-06-04 03:56:53,575 - database - ERROR - Failed to connect to database
2025-06-04 03:56:58,587 - database - ERROR - Error connecting to MongoDB: localhost:27017: [WinError 10061] No connection could be made because the target machine actively refused it (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 683f6fa5ed6558002f9c89ec, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [WinError 10061] No connection could be made because the target machine actively refused it (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms)')>]>
2025-06-04 03:56:58,588 - database - ERROR - Failed to connect to database
2025-06-04 03:57:03,600 - database - ERROR - Error connecting to MongoDB: localhost:27017: [WinError 10061] No connection could be made because the target machine actively refused it (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 683f6faaed6558002f9c89ed, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [WinError 10061] No connection could be made because the target machine actively refused it (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms)')>]>
2025-06-04 03:57:03,600 - database - ERROR - Failed to connect to database
2025-06-04 03:57:08,611 - database - ERROR - Error connecting to MongoDB: localhost:27017: [WinError 10061] No connection could be made because the target machine actively refused it (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 683f6fafed6558002f9c89ee, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [WinError 10061] No connection could be made because the target machine actively refused it (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms)')>]>
2025-06-04 03:57:08,611 - database - ERROR - Failed to connect to database
2025-06-04 03:57:13,630 - database - ERROR - Error connecting to MongoDB: localhost:27017: [WinError 10061] No connection could be made because the target machine actively refused it (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 683f6fb4ed6558002f9c89ef, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [WinError 10061] No connection could be made because the target machine actively refused it (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms)')>]>
2025-06-04 03:57:13,630 - database - ERROR - Failed to connect to database
2025-06-04 03:57:18,641 - database - ERROR - Error connecting to MongoDB: localhost:27017: [WinError 10061] No connection could be made because the target machine actively refused it (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 683f6fb9ed6558002f9c89f0, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [WinError 10061] No connection could be made because the target machine actively refused it (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms)')>]>
2025-06-04 03:57:18,641 - database - ERROR - Failed to connect to database
