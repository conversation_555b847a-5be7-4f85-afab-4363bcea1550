#!/usr/bin/env python3
"""
Test the bot's performance stats with enhanced database connection
"""

import asyncio
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s:%(name)s:%(message)s')

async def test_bot_performance_stats():
    """Test the bot's performance stats functionality"""
    print("🧪 Testing Bot Performance Stats with Enhanced Database...")
    print("=" * 60)
    
    try:
        # Import bot modules
        from bot import PERFORMANCE_OPTIMIZATIONS_ENABLED
        from memory_manager import get_memory_manager
        
        print(f"Performance optimizations enabled: {PERFORMANCE_OPTIMIZATIONS_ENABLED}")
        
        if PERFORMANCE_OPTIMIZATIONS_ENABLED:
            # Import performance modules
            from performance_manager import get_performance_manager
            from enhanced_database import get_enhanced_db_manager
            from discord_api_optimizer import get_discord_api_optimizer
            
            print("\n1. Starting performance systems...")
            
            # Start performance manager
            performance_manager = get_performance_manager()
            await performance_manager.start()
            print("   ✅ Performance manager started")
            
            # Start enhanced database
            enhanced_db = get_enhanced_db_manager()
            await enhanced_db.start()
            
            # Ensure connection
            connection_ok = await enhanced_db.ensure_connection()
            print(f"   {'✅' if connection_ok else '❌'} Enhanced database: {'Connected' if connection_ok else 'Disconnected'}")
            
            # Start API optimizer
            api_optimizer = get_discord_api_optimizer()
            await api_optimizer.start()
            print("   ✅ API optimizer started")
            
            print("\n2. Testing performance stats generation...")
            
            # Get all stats like the performance_stats command does
            try:
                perf_metrics = performance_manager.get_metrics()
                db_stats = enhanced_db.get_performance_stats()
                api_stats = api_optimizer.get_comprehensive_stats()
                
                print("   ✅ Performance metrics retrieved")
                print("   ✅ Database stats retrieved")
                print("   ✅ API stats retrieved")
                
                # Display key metrics
                print("\n3. Performance Metrics Summary:")
                print(f"   📊 Total Requests: {perf_metrics['total_requests']:,}")
                print(f"   📊 Success Rate: {perf_metrics['success_rate']:.1f}%")
                print(f"   📊 Current Memory: {perf_metrics['current_memory_mb']:.1f}MB")
                
                print("\n4. Database Performance:")
                db_conn_stats = db_stats['connection_pool']
                db_cache_stats = db_stats['query_cache']
                print(f"   🗄️ Connection Status: {'✅ Connected' if db_conn_stats['is_connected'] else '❌ Disconnected'}")
                print(f"   🗄️ Query Count: {db_conn_stats['query_count']:,}")
                print(f"   🗄️ Cache Hit Rate: {db_cache_stats['hit_rate']:.1f}%")
                print(f"   🗄️ Cache Size: {db_cache_stats['size']:,}/{db_cache_stats['max_size']:,}")
                
                print("\n5. API Optimization:")
                rate_stats = api_stats['rate_limiter']
                bulk_stats = api_stats['bulk_manager']
                print(f"   🔄 Requests Made: {rate_stats['requests_made']:,}")
                print(f"   🔄 Operations Batched: {bulk_stats['operations_batched']:,}")
                
            except Exception as e:
                print(f"   ❌ Error getting stats: {e}")
                return False
            
            # Test memory manager stats
            print("\n6. Memory Management:")
            memory_manager = get_memory_manager()
            memory_stats = memory_manager.get_stats()
            print(f"   🧠 Current Usage: {memory_stats['current_memory_mb']:.1f}MB")
            print(f"   🧠 Peak Usage: {memory_stats['peak_memory_mb']:.1f}MB")
            print(f"   🧠 GC Collections: {memory_stats['gc_collections']:,}")
            
            # Cleanup
            print("\n7. Cleanup...")
            await performance_manager.stop()
            await enhanced_db.stop()
            await api_optimizer.stop()
            print("   ✅ All systems stopped")
            
        else:
            print("⚠️ Performance optimizations not enabled")
            return False
        
        print("\n" + "=" * 60)
        print("🎉 Bot performance stats test completed successfully!")
        print("✅ The /performance_stats command will show correct database status")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function"""
    print("🔧 Bot Performance Stats Test Suite")
    print("Testing performance monitoring with enhanced database...")
    print()
    
    try:
        success = await test_bot_performance_stats()
        
        if success:
            print("\n🎯 Result: PERFORMANCE STATS WORKING PERFECTLY")
            print("✅ Enhanced database connection is working")
            print("✅ /performance_stats command will show ✅ Connected status")
        else:
            print("\n❌ Result: PERFORMANCE STATS NEED ATTENTION")
            
    except Exception as e:
        print(f"\n💥 Test suite failed: {e}")

if __name__ == "__main__":
    asyncio.run(main())
