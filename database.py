import motor.motor_asyncio
from datetime import datetime
import asyncio
import logging
import time

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('database')

# MongoDB connection pooling and caching
class DatabaseManager:
    """Singleton class to manage MongoDB connections and provide caching"""
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(DatabaseManager, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        if self._initialized:
            return

        # Connection settings with proper pooling
        self.mongo_uri = "mongodb://localhost:27017/"
        self.db_name = "missminutesbot"
        self.client = None
        self.db = None

        # Collection references
        self.collections = {}

        # Cache settings
        self.cache = {}
        self.cache_ttl = {}
        self.default_ttl = 300  # 5 minutes default TTL
        self.max_cache_size = 1000  # Maximum number of items in cache

        # Connection state
        self.is_connected = False
        self.last_connection_attempt = 0
        self.connection_retry_delay = 5  # seconds
        self.connection_lock = asyncio.Lock()

        self._initialized = True

    async def connect(self, force=False):
        """Connect to MongoDB with connection pooling"""
        # Use a lock to prevent multiple connection attempts
        async with self.connection_lock:
            # Check if already connected
            if self.is_connected and not force:
                return True

            # Implement connection throttling
            current_time = time.time()
            if not force and current_time - self.last_connection_attempt < self.connection_retry_delay:
                logger.warning("Connection attempt throttled. Waiting before retry.")
                return False

            self.last_connection_attempt = current_time

            try:
                # Configure connection with proper pooling
                self.client = motor.motor_asyncio.AsyncIOMotorClient(
                    self.mongo_uri,
                    maxPoolSize=50,  # Increase connection pool size
                    minPoolSize=5,   # Maintain minimum connections
                    maxIdleTimeMS=30000,  # Close idle connections after 30 seconds
                    connectTimeoutMS=5000,  # 5 second connection timeout
                    serverSelectionTimeoutMS=5000,  # 5 second server selection timeout
                    retryWrites=True  # Enable retryable writes
                )

                # Test connection
                await self.client.admin.command('ping')

                self.db = self.client[self.db_name]
                self.is_connected = True

                # Initialize collections
                self.collections = {
                    "gangs": self.db["gangs"],
                    "applications": self.db["applications"],
                    "settings": self.db["settings"],
                    "sticky_messages": self.db["sticky_messages"],
                    "tebex_settings": self.db["tebex_settings"],
                    "reaction_roles": self.db["reaction_roles"],
                    "transactions": self.db["transactions"],
                    "guild_settings": self.db["guild_settings"]
                }

                # Create indexes
                await self.collections["gangs"].create_index("guild_id")
                await self.collections["applications"].create_index("guild_id")
                await self.collections["settings"].create_index("guild_id")
                await self.collections["transactions"].create_index([("transaction_id", 1)], unique=True)
                await self.collections["guild_settings"].create_index("guild_id", unique=True)

                logger.info("Successfully connected to MongoDB with connection pooling")
                return True

            except Exception as e:
                self.is_connected = False
                logger.error(f"Error connecting to MongoDB: {e}")
                return False

    def get_collection(self, name):
        """Get a collection by name, ensuring connection first"""
        if name in self.collections:
            return self.collections[name]
        return None

    async def ensure_connection(self):
        """Ensure database is connected before operations"""
        if not self.is_connected:
            return await self.connect()
        return True

    def cache_get(self, key):
        """Get an item from cache if it exists and is not expired"""
        if key in self.cache and key in self.cache_ttl:
            if self.cache_ttl[key] > time.time():
                return self.cache[key]
            else:
                # Remove expired item
                del self.cache[key]
                del self.cache_ttl[key]
        return None

    def cache_set(self, key, value, ttl=None):
        """Store an item in cache with TTL"""
        # Manage cache size
        if len(self.cache) >= self.max_cache_size:
            # Remove oldest items
            oldest_keys = sorted(self.cache_ttl.items(), key=lambda x: x[1])[:100]
            for old_key, _ in oldest_keys:
                if old_key in self.cache:
                    del self.cache[old_key]
                if old_key in self.cache_ttl:
                    del self.cache_ttl[old_key]

        # Set new cache item
        self.cache[key] = value
        self.cache_ttl[key] = time.time() + (ttl or self.default_ttl)

    def cache_invalidate(self, key_prefix=None):
        """Invalidate cache items by prefix or all if None"""
        if key_prefix is None:
            self.cache.clear()
            self.cache_ttl.clear()
        else:
            keys_to_remove = [k for k in self.cache if k.startswith(key_prefix)]
            for key in keys_to_remove:
                if key in self.cache:
                    del self.cache[key]
                if key in self.cache_ttl:
                    del self.cache_ttl[key]

# Create global database manager instance
db_manager = DatabaseManager()

# Initialize collections for backward compatibility
MONGO_CLIENT = None
DB = None
GANGS_COLLECTION = None
APPLICATIONS_COLLECTION = None
SETTINGS_COLLECTION = None
STICKY_MESSAGES_COLLECTION = None
TEBEX_SETTINGS_COLLECTION = None
REACTION_ROLES_COLLECTION = None
TRANSACTIONS_COLLECTION = None
GUILD_SETTINGS_COLLECTION = None

async def init_db():
    """Initialize database connection and setup"""
    global MONGO_CLIENT, DB, GANGS_COLLECTION, APPLICATIONS_COLLECTION, SETTINGS_COLLECTION
    global STICKY_MESSAGES_COLLECTION, TEBEX_SETTINGS_COLLECTION, REACTION_ROLES_COLLECTION
    global TRANSACTIONS_COLLECTION, GUILD_SETTINGS_COLLECTION

    # Connect using the manager
    success = await db_manager.connect()
    if not success:
        return False

    # Set global variables for backward compatibility
    MONGO_CLIENT = db_manager.client
    DB = db_manager.db
    GANGS_COLLECTION = db_manager.get_collection("gangs")
    APPLICATIONS_COLLECTION = db_manager.get_collection("applications")
    SETTINGS_COLLECTION = db_manager.get_collection("settings")
    STICKY_MESSAGES_COLLECTION = db_manager.get_collection("sticky_messages")
    TEBEX_SETTINGS_COLLECTION = db_manager.get_collection("tebex_settings")
    REACTION_ROLES_COLLECTION = db_manager.get_collection("reaction_roles")
    TRANSACTIONS_COLLECTION = db_manager.get_collection("transactions")
    GUILD_SETTINGS_COLLECTION = db_manager.get_collection("guild_settings")

    # Initialize default documents if they don't exist
    try:
        collections_init = {
            GANGS_COLLECTION: {"_id": "gangs", "roles": {}, "members": {}, "leaders": {}, "strikes": {}},
            APPLICATIONS_COLLECTION: {"_id": "applications", "forms": {}, "channels": {}, "status": {}},
            SETTINGS_COLLECTION: {"_id": "settings", "welcome": {}, "vanity": {}, "notifications": {}, "join_role_id": None},
            STICKY_MESSAGES_COLLECTION: {"_id": "sticky_messages", "messages": {}},
            TEBEX_SETTINGS_COLLECTION: {"_id": "tebex_settings", "channel_id": None, "webhook_url": None},
            REACTION_ROLES_COLLECTION: {"_id": "reaction_roles", "roles": {}, "message_id": None, "channel_id": None}
        }

        for collection, default_doc in collections_init.items():
            existing_doc = await collection.find_one({"_id": default_doc["_id"]})
            if not existing_doc:
                await collection.insert_one(default_doc)

        logger.info("Successfully connected to MongoDB and initialized collections")
    except Exception as e:
        logger.error(f"Error initializing collections: {e}")
        return False

    return True

async def save_guild_settings(guild_id, settings):
    """Save guild settings with caching"""
    try:
        # Ensure database connection
        if not await db_manager.ensure_connection():
            logger.error("Failed to connect to database")
            return False

        collection = db_manager.get_collection("guild_settings")
        if collection is None:
            logger.error("Guild settings collection not available")
            return False

        # Update the database
        await collection.update_one(
            {"guild_id": guild_id},
            {"$set": settings},
            upsert=True
        )

        # Invalidate cache for this guild
        db_manager.cache_invalidate(f"guild_settings:{guild_id}")

        return True
    except Exception as e:
        logger.error(f"Error saving guild settings: {e}")
        return False

async def get_guild_settings(guild_id):
    """Get guild settings with caching"""
    try:
        # Check cache first
        cache_key = f"guild_settings:{guild_id}"
        cached_data = db_manager.cache_get(cache_key)
        if cached_data is not None:
            return cached_data

        # Ensure database connection
        if not await db_manager.ensure_connection():
            logger.error("Failed to connect to database")
            return {}

        collection = db_manager.get_collection("guild_settings")
        if collection is None:
            logger.error("Guild settings collection not available")
            return {}

        # Get from database
        settings = await collection.find_one({"guild_id": guild_id}, {"_id": 0})
        result = settings if settings else {}

        # Cache the result
        db_manager.cache_set(cache_key, result)

        return result
    except Exception as e:
        logger.error(f"Error loading guild settings: {e}")
        return {}

async def save_data(data):
    """Save bot data with optimized connection handling and caching"""
    try:
        # Ensure database connection
        if not await db_manager.ensure_connection():
            logger.error("Failed to connect to database")
            return False

        # Process data in batches for better performance
        try:
            # Process gang roles data to ensure consistent string IDs
            processed_roles = {}
            for gang_name, gang_data in data["gangs"].get("roles", {}).items():
                processed_gang_data = gang_data.copy()
                processed_gang_data["leader"] = str(gang_data["leader"])
                processed_gang_data["leader_role"] = str(gang_data["leader_role"])
                processed_gang_data["members"] = [str(m) if isinstance(m, (int, str)) else m for m in gang_data["members"]]
                processed_roles[gang_name] = processed_gang_data

            gangs_data = {
                "_id": "gangs",
                "roles": processed_roles,
                "leaders": {str(k): str(v) for k, v in data["gangs"].get("leaders", {}).items()},
                "strikes": data["gangs"].get("strikes", {}),
                "invitations": data["gangs"].get("invitations", {})  # Save gang invitations for persistence
            }

            # Create a list of operations to execute in parallel
            operations = []

            # Add each collection update as a separate task
            operations.append(db_manager.get_collection("gangs").replace_one(
                {"_id": "gangs"}, gangs_data, upsert=True
            ))

            # Process applications data to ensure all keys are strings
            applications_data = data["applications"].copy()
            if "status" in applications_data:
                # Convert all user IDs to strings in the status dictionary
                applications_data["status"] = {
                    str(user_id): status_data
                    for user_id, status_data in applications_data["status"].items()
                }

            operations.append(db_manager.get_collection("applications").replace_one(
                {"_id": "applications"},
                {"_id": "applications", **applications_data},
                upsert=True
            ))

            operations.append(db_manager.get_collection("settings").replace_one(
                {"_id": "settings"},
                {"_id": "settings", **data["settings"]},
                upsert=True
            ))

            operations.append(db_manager.get_collection("sticky_messages").replace_one(
                {"_id": "sticky_messages"},
                {"_id": "sticky_messages", "messages": {str(k): v for k, v in data["sticky_messages"].items()}},
                upsert=True
            ))

            operations.append(db_manager.get_collection("tebex_settings").replace_one(
                {"_id": "tebex_settings"},
                {"_id": "tebex_settings", **data["tebex_settings"]},
                upsert=True
            ))

            # Process reaction_roles data to ensure consistent structure
            reaction_roles_data = {"_id": "reaction_roles"}

            # Extract the roles and config from the data
            if "reaction_roles" in data:
                # Add message_id and channel_id
                if "message_id" in data["reaction_roles"]:
                    reaction_roles_data["message_id"] = data["reaction_roles"]["message_id"]
                if "channel_id" in data["reaction_roles"]:
                    reaction_roles_data["channel_id"] = data["reaction_roles"]["channel_id"]

                # Process roles - ensure they're in a flat structure
                roles = {}
                if "roles" in data["reaction_roles"]:
                    if isinstance(data["reaction_roles"]["roles"], dict):
                        # Check if it's already a flat emoji->role_id mapping
                        has_flat_structure = all(
                            isinstance(k, str) and isinstance(v, (int, str)) and k not in ('config', 'roles')
                            for k, v in data["reaction_roles"]["roles"].items()
                        )

                        if has_flat_structure:
                            roles = data["reaction_roles"]["roles"]
                        # If it's nested, try to extract the emoji->role_id mapping
                        elif "roles" in data["reaction_roles"]["roles"] and isinstance(data["reaction_roles"]["roles"]["roles"], dict):
                            roles = data["reaction_roles"]["roles"]["roles"]

                reaction_roles_data["roles"] = roles

                # Process config
                config = {"allow_multiple": False}  # Default config
                if "config" in data["reaction_roles"]:
                    config = data["reaction_roles"]["config"]
                elif "roles" in data["reaction_roles"] and "config" in data["reaction_roles"]["roles"]:
                    config = data["reaction_roles"]["roles"]["config"]

                reaction_roles_data["config"] = config

            operations.append(db_manager.get_collection("reaction_roles").replace_one(
                {"_id": "reaction_roles"},
                reaction_roles_data,
                upsert=True
            ))

            # Execute all operations in parallel
            await asyncio.gather(*operations)

            # Invalidate all related caches
            db_manager.cache_invalidate("bot_data")

            logger.info("Data saved successfully to MongoDB")
            return True

        except Exception as e:
            logger.error(f"Error processing data: {e}")
            return False

    except Exception as e:
        logger.error(f"Error saving data to MongoDB: {e}")
        return False

async def load_data():
    """Load bot data with caching for better performance"""
    try:
        # Check cache first
        cache_key = "bot_data:all"
        cached_data = db_manager.cache_get(cache_key)
        if cached_data is not None:
            logger.info("Loaded data from cache")
            return cached_data

        # Ensure database connection
        if not await db_manager.ensure_connection():
            logger.error("Failed to connect to database")
            return None

        # Create tasks for parallel execution
        tasks = [
            db_manager.get_collection("gangs").find_one({"_id": "gangs"}, {"_id": 0}),
            db_manager.get_collection("applications").find_one({"_id": "applications"}, {"_id": 0}),
            db_manager.get_collection("settings").find_one({"_id": "settings"}, {"_id": 0}),
            db_manager.get_collection("sticky_messages").find_one({"_id": "sticky_messages"}, {"_id": 0}),
            db_manager.get_collection("tebex_settings").find_one({"_id": "tebex_settings"}, {"_id": 0}),
            db_manager.get_collection("reaction_roles").find_one({"_id": "reaction_roles"}, {"_id": 0})
        ]

        # Execute all queries in parallel
        results = await asyncio.gather(*tasks)

        # Process results
        data = {
            "gangs": results[0] or {},
            "applications": results[1] or {},
            "settings": results[2] or {},
            "sticky_messages": (results[3] or {}).get("messages", {}),
            "tebex_settings": results[4] or {},
            "reaction_roles": results[5] or {}
        }

        # Cache the result
        db_manager.cache_set(cache_key, data, ttl=300)  # Cache for 5 minutes

        logger.info("Data loaded successfully from MongoDB")
        return data
    except Exception as e:
        logger.error(f"Error loading data from MongoDB: {e}")
        return None

async def save_transaction(transaction_data):
    """Save transaction data with optimized connection handling"""
    try:
        # Ensure database connection
        if not await db_manager.ensure_connection():
            logger.error("Failed to connect to database")
            return False

        collection = db_manager.get_collection("transactions")
        if collection is None:
            logger.error("Transactions collection not available")
            return False

        # Add timestamp if not present
        if "timestamp" not in transaction_data:
            transaction_data["timestamp"] = datetime.now()

        # Check if this is an update (transaction with this ID already exists)
        transaction_id = transaction_data.get('transaction_id')
        is_update = False

        if transaction_id:
            # Check if transaction already exists
            existing = await collection.find_one({"transaction_id": transaction_id})
            is_update = existing is not None

        # Insert or update with retry logic
        for attempt in range(3):  # Try up to 3 times
            try:
                if is_update:
                    # Update existing transaction
                    await collection.replace_one(
                        {"transaction_id": transaction_id},
                        transaction_data
                    )
                else:
                    # Insert new transaction
                    await collection.insert_one(transaction_data)

                # Invalidate transactions cache
                db_manager.cache_invalidate("transactions")

                return True
            except Exception as e:
                if attempt < 2:  # Don't sleep on the last attempt
                    await asyncio.sleep(0.5 * (attempt + 1))  # Exponential backoff
                else:
                    logger.error(f"Failed to save transaction after retries: {e}")
                    return False

    except Exception as e:
        logger.error(f"Error saving transaction to MongoDB: {e}")
        return False

async def get_transactions(limit=100, skip=0, sort_by="timestamp", sort_dir=-1):
    """Get transactions with pagination, sorting and caching"""
    try:
        # Check cache for common queries
        if limit == 100 and skip == 0 and sort_by == "timestamp" and sort_dir == -1:
            cache_key = "transactions:recent"
            cached_data = db_manager.cache_get(cache_key)
            if cached_data is not None:
                return cached_data

        # Ensure database connection
        if not await db_manager.ensure_connection():
            logger.error("Failed to connect to database")
            return []

        collection = db_manager.get_collection("transactions")
        if collection is None:
            logger.error("Transactions collection not available")
            return []

        # Query with pagination and sorting
        cursor = collection.find({}, {"_id": 0})

        # Apply sorting
        cursor = cursor.sort(sort_by, sort_dir)

        # Apply pagination
        if skip > 0:
            cursor = cursor.skip(skip)
        if limit > 0:
            cursor = cursor.limit(limit)

        # Execute query
        transactions = await cursor.to_list(length=limit)

        # Cache only for standard queries
        if limit == 100 and skip == 0 and sort_by == "timestamp" and sort_dir == -1:
            db_manager.cache_set("transactions:recent", transactions, ttl=60)  # Cache for 1 minute

        return transactions
    except Exception as e:
        logger.error(f"Error getting transactions from MongoDB: {e}")
        return []